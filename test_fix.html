<!DOCTYPE html>
<html>
<head>
    <title>Test Password Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, button { padding: 10px; margin: 5px 0; }
        button { background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 15px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Password Generator Test</h1>
        
        <div class="form-group">
            <label>Password Length:</label>
            <input type="range" id="length" min="4" max="64" value="12" oninput="updateLength()">
            <span id="lengthDisplay">12</span>
        </div>
        
        <div class="form-group">
            <label>Character Types:</label>
            <label><input type="checkbox" id="lowercase" checked> Lowercase</label>
            <label><input type="checkbox" id="uppercase" checked> Uppercase</label>
            <label><input type="checkbox" id="digits" checked> Digits</label>
            <label><input type="checkbox" id="symbols" checked> Symbols</label>
        </div>
        
        <button onclick="testGenerate()">Generate Password</button>
        <button onclick="testConnection()">Test Connection</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function updateLength() {
            const length = document.getElementById('length').value;
            document.getElementById('lengthDisplay').textContent = length;
        }

        function showResult(message, isError = false) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = 'result ' + (isError ? 'error' : 'success');
            result.style.display = 'block';
        }

        async function testConnection() {
            try {
                const response = await fetch('/test');
                if (response.ok) {
                    const data = await response.json();
                    showResult('✅ Connection successful: ' + data.message);
                } else {
                    showResult('❌ Connection failed: ' + response.status, true);
                }
            } catch (error) {
                showResult('❌ Connection error: ' + error.message, true);
            }
        }

        async function testGenerate() {
            const data = {
                length: parseInt(document.getElementById('length').value),
                lowercase: document.getElementById('lowercase').checked,
                uppercase: document.getElementById('uppercase').checked,
                digits: document.getElementById('digits').checked,
                symbols: document.getElementById('symbols').checked,
                exclude: ''
            };

            try {
                const response = await fetch('/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult(`✅ Password generated: ${result.password}\nStrength: ${result.strength.level} (${result.strength.score}/100)`);
                } else {
                    showResult('❌ Generation failed: ' + result.error, true);
                }
            } catch (error) {
                showResult('❌ Request failed: ' + error.message, true);
            }
        }

        // Test connection on load
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
