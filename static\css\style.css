body {
    font-family: Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 30px;
    min-height: 100vh;
    font-size: 18px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
}

.header {
    text-align: center;
    color: white;
    margin-bottom: 50px;
}

.header h1 {
    font-size: 4em;
    margin-bottom: 20px;
}

.card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 50px;
    margin-bottom: 30px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.card h2 {
    font-size: 2.2em;
    margin-bottom: 30px;
    color: #333;
}

.row {
    display: flex;
    gap: 30px;
}

.col {
    flex: 1;
}

.form-group {
    margin-bottom: 30px;
}

label {
    display: block;
    margin-bottom: 12px;
    font-weight: bold;
    color: #333;
    font-size: 1.2em;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 25px;
}

.slider {
    flex: 1;
    height: 12px;
    border-radius: 8px;
    background: #ddd;
    outline: none;
}

.length-display {
    background: #667eea;
    color: white;
    padding: 15px 25px;
    border-radius: 25px;
    font-weight: bold;
    min-width: 80px;
    text-align: center;
    font-size: 1.4em;
}

.checkbox-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.checkbox-item input[type="checkbox"] {
    width: 24px;
    height: 24px;
    accent-color: #667eea;
}

.checkbox-item label {
    font-size: 1.1em;
    margin-bottom: 0;
}

input[type="text"], input[type="password"], input[type="number"] {
    width: 100%;
    padding: 18px;
    border: 3px solid #ddd;
    border-radius: 12px;
    font-size: 1.2em;
    box-sizing: border-box;
}

input[type="text"]:focus, input[type="password"]:focus, input[type="number"]:focus {
    outline: none;
    border-color: #667eea;
}

.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 20px 40px;
    border-radius: 12px;
    font-size: 1.4em;
    font-weight: bold;
    cursor: pointer;
    width: 100%;
    margin-bottom: 15px;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.password-display {
    background: #f8f9fa;
    border: 3px solid #ddd;
    border-radius: 15px;
    padding: 25px;
    font-family: monospace;
    font-size: 1.6em;
    font-weight: bold;
    text-align: center;
    margin-bottom: 25px;
    word-break: break-all;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-display.generated {
    background: #e8f5e8;
    border-color: #28a745;
    color: #155724;
}

.strength-meter {
    margin-bottom: 30px;
}

.strength-bar {
    width: 100%;
    height: 30px;
    background: #ddd;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 15px;
}

.strength-fill {
    height: 100%;
    border-radius: 15px;
    transition: width 0.5s ease, background-color 0.5s ease;
    width: 0%;
}

.strength-fill.very-weak {
    background: #dc3545;
}

.strength-fill.weak {
    background: #fd7e14;
}

.strength-fill.medium {
    background: #ffc107;
}

.strength-fill.strong {
    background: #20c997;
}

.strength-fill.very-strong {
    background: #28a745;
}

.strength-text {
    text-align: center;
    font-weight: bold;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.feedback {
    background: #f8f9fa;
    border-left: 6px solid #667eea;
    padding: 25px;
    border-radius: 10px;
    font-size: 1.1em;
}

.enhancement-results {
    display: none;
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.comparison-item h4 {
    margin-bottom: 10px;
}

.comparison-item .password {
    font-family: monospace;
    padding: 10px;
    background: white;
    border-radius: 5px;
    border: 2px solid #ddd;
    word-break: break-all;
}

.comparison-item.before .password {
    border-color: #dc3545;
    color: #dc3545;
}

.comparison-item.after .password {
    border-color: #28a745;
    color: #28a745;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #28a745;
}

.notification.error {
    background: #dc3545;
}

@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }
    
    .checkbox-group {
        grid-template-columns: 1fr;
    }
    
    .comparison {
        grid-template-columns: 1fr;
    }
}
