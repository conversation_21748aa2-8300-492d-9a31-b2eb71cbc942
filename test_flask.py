#!/usr/bin/env python3
"""
Test script for Flask password generator.
"""

import requests
import json

def test_password_generation():
    """Test password generation endpoint."""
    url = "http://localhost:5000/generate"
    
    data = {
        "length": 12,
        "lowercase": True,
        "uppercase": True,
        "digits": True,
        "symbols": True,
        "exclude": ""
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Password generated: {result['password']}")
                print(f"✅ Strength: {result['strength']['level']} ({result['strength']['score']}/100)")
                return True
            else:
                print(f"❌ Generation failed: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Is the Flask app running?")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_enhancement():
    """Test password enhancement endpoint."""
    url = "http://localhost:5000/enhance"
    
    data = {
        "password": "password123",
        "target_length": 16
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"\nEnhancement Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Original: {result['original']['password']}")
                print(f"✅ Enhanced: {result['enhanced']['password']}")
                print(f"✅ Improvement: +{result['improvement']} points")
                return True
            else:
                print(f"❌ Enhancement failed: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Enhancement error: {e}")
        return False

def main():
    """Run tests."""
    print("🧪 TESTING FLASK PASSWORD GENERATOR")
    print("=" * 40)
    
    print("\n1. Testing password generation...")
    gen_success = test_password_generation()
    
    print("\n2. Testing password enhancement...")
    enh_success = test_enhancement()
    
    print("\n" + "=" * 40)
    if gen_success and enh_success:
        print("✅ ALL TESTS PASSED!")
        print("🌐 Web interface should be working at: http://localhost:5000")
    else:
        print("❌ Some tests failed. Check the Flask app.")

if __name__ == "__main__":
    main()
