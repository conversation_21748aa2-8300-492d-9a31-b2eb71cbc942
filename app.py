#!/usr/bin/env python3
"""
Flask Web Application for Password Generator
Modern web interface with beautiful UI and responsive design.
"""

from flask import Flask, render_template, request, jsonify
import random
import string
import json
from datetime import datetime
from password_generator_cli import PasswordGenerator

app = Flask(__name__)

class WebPasswordGenerator:
    """Enhanced password generator for web interface."""
    
    def __init__(self):
        self.cli_generator = PasswordGenerator()
        self.lowercase = string.ascii_lowercase
        self.uppercase = string.ascii_uppercase
        self.digits = string.digits
        self.symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    def generate_password(self, length=12, use_lowercase=True, use_uppercase=True,
                         use_digits=True, use_symbols=True, exclude_chars=""):
        """Generate password using the CLI generator."""
        try:
            password = self.cli_generator.generate_password(
                length=length,
                use_lowercase=use_lowercase,
                use_uppercase=use_uppercase,
                use_digits=use_digits,
                use_symbols=use_symbols,
                exclude_chars=exclude_chars
            )
            return password
        except ValueError as e:
            raise ValueError(str(e))
    
    def analyze_strength(self, password):
        """Analyze password strength and return score with feedback."""
        if not password:
            return 0, "No Password", ["Enter a password to analyze"]
        
        score = 0
        feedback = []
        
        # Length scoring
        length = len(password)
        if length >= 12:
            score += 25
        elif length >= 8:
            score += 15
            feedback.append("Consider using at least 12 characters")
        else:
            score += 5
            feedback.append("Password is too short. Use at least 8 characters")
        
        # Character variety scoring
        has_lower = any(c.islower() for c in password)
        has_upper = any(c.isupper() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_symbol = any(c in self.symbols for c in password)
        
        char_types = sum([has_lower, has_upper, has_digit, has_symbol])
        score += char_types * 15
        
        if char_types < 3:
            feedback.append("Use a mix of uppercase, lowercase, numbers, and symbols")
        
        # Pattern detection
        if len(set(password)) < len(password) * 0.7:
            score -= 10
            feedback.append("Avoid repeating characters")
        
        # Sequential patterns
        if any(str(i) + str(i+1) + str(i+2) in password for i in range(8)):
            score -= 15
            feedback.append("Avoid sequential numbers")
        
        # Common words
        common_words = ['password', 'admin', 'user', 'login', 'welcome', '123456', 'qwerty']
        for word in common_words:
            if word.lower() in password.lower():
                score -= 20
                feedback.append(f"Avoid common words like '{word}'")
                break
        
        # Determine strength level
        score = max(0, min(100, score))
        if score >= 80:
            strength = "Very Strong"
        elif score >= 60:
            strength = "Strong"
        elif score >= 40:
            strength = "Medium"
        elif score >= 20:
            strength = "Weak"
        else:
            strength = "Very Weak"
        
        return score, strength, feedback

# Initialize generator
generator = WebPasswordGenerator()

@app.route('/')
def index():
    """Main page."""
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate_password():
    """Generate password endpoint."""
    try:
        data = request.get_json()
        
        # Extract parameters
        length = int(data.get('length', 12))
        use_lowercase = data.get('lowercase', True)
        use_uppercase = data.get('uppercase', True)
        use_digits = data.get('digits', True)
        use_symbols = data.get('symbols', True)
        exclude_chars = data.get('exclude', '')
        
        # Generate password
        password = generator.generate_password(
            length=length,
            use_lowercase=use_lowercase,
            use_uppercase=use_uppercase,
            use_digits=use_digits,
            use_symbols=use_symbols,
            exclude_chars=exclude_chars
        )
        
        # Analyze strength
        score, strength, feedback = generator.analyze_strength(password)
        
        return jsonify({
            'success': True,
            'password': password,
            'strength': {
                'score': score,
                'level': strength,
                'feedback': feedback
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400

@app.route('/analyze', methods=['POST'])
def analyze_password():
    """Analyze password strength endpoint."""
    try:
        data = request.get_json()
        password = data.get('password', '')
        
        score, strength, feedback = generator.analyze_strength(password)
        
        return jsonify({
            'success': True,
            'strength': {
                'score': score,
                'level': strength,
                'feedback': feedback
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400

@app.route('/enhance', methods=['POST'])
def enhance_password():
    """Enhance weak password endpoint."""
    try:
        data = request.get_json()
        original = data.get('password', '')
        target_length = int(data.get('target_length', 16))
        
        if not original:
            return jsonify({
                'success': False,
                'error': 'Please provide a password to enhance'
            }), 400
        
        # Simple enhancement logic
        enhanced = enhance_weak_password(original, target_length)
        
        # Analyze both passwords
        orig_score, orig_strength, orig_feedback = generator.analyze_strength(original)
        enh_score, enh_strength, enh_feedback = generator.analyze_strength(enhanced)
        
        return jsonify({
            'success': True,
            'original': {
                'password': original,
                'score': orig_score,
                'level': orig_strength,
                'feedback': orig_feedback
            },
            'enhanced': {
                'password': enhanced,
                'score': enh_score,
                'level': enh_strength,
                'feedback': enh_feedback
            },
            'improvement': enh_score - orig_score
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400

def enhance_weak_password(password, target_length):
    """Simple password enhancement logic."""
    enhanced = password
    
    # Character substitutions
    substitutions = {
        'a': '@', 'e': '3', 'i': '!', 'o': '0', 's': '$', 't': '7'
    }
    
    # Apply some substitutions
    for char, sub in substitutions.items():
        if char in enhanced.lower():
            enhanced = enhanced.replace(char, sub, 1)
            enhanced = enhanced.replace(char.upper(), sub, 1)
    
    # Add complexity if too short
    while len(enhanced) < target_length:
        if len(enhanced) % 4 == 0:
            enhanced += random.choice('!@#$%^&*')
        elif len(enhanced) % 4 == 1:
            enhanced += random.choice(string.ascii_uppercase)
        elif len(enhanced) % 4 == 2:
            enhanced += random.choice(string.digits)
        else:
            enhanced += random.choice(string.ascii_lowercase)
    
    # Ensure we have mixed case
    if enhanced.islower():
        enhanced = enhanced[0].upper() + enhanced[1:]
    
    return enhanced

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
