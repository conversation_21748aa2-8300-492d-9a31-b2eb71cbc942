#!/usr/bin/env python3
"""
Quick example showing password generation without user interaction.
"""

from password_generator_cli import PasswordGenerator
from password_generator_gui import AdvancedPasswordGenerator, PasswordStrengthAnalyzer

def main():
    print("🔐 PASSWORD GENERATOR - QUICK EXAMPLES")
    print("=" * 50)
    
    # Create generators
    basic_gen = PasswordGenerator()
    advanced_gen = AdvancedPasswordGenerator()
    analyzer = PasswordStrengthAnalyzer()
    
    print("\n1. BASIC PASSWORDS (Different Lengths):")
    for length in [8, 12, 16, 20]:
        password = basic_gen.generate_password(length=length)
        score, strength, _ = analyzer.analyze_strength(password)
        print(f"   Length {length:2d}: {password:<25} ({strength}, {score}/100)")
    
    print("\n2. CHARACTER TYPE VARIATIONS:")
    
    # Only letters
    password = basic_gen.generate_password(length=16, use_digits=False, use_symbols=False)
    print(f"   Letters only:  {password}")
    
    # No symbols
    password = basic_gen.generate_password(length=16, use_symbols=False)
    print(f"   No symbols:    {password}")
    
    # Only digits and symbols
    password = basic_gen.generate_password(length=16, use_lowercase=False, use_uppercase=False)
    print(f"   Digits+symbols: {password}")
    
    print("\n3. ADVANCED SECURE PASSWORDS:")
    
    # Standard secure
    password = advanced_gen.generate_secure_password(length=16)
    score, strength, _ = analyzer.analyze_strength(password)
    print(f"   Standard:      {password} ({strength}, {score}/100)")
    
    # No similar characters
    password = advanced_gen.generate_secure_password(length=16, exclude_similar=True)
    score, strength, _ = analyzer.analyze_strength(password)
    print(f"   No similar:    {password} ({strength}, {score}/100)")
    
    # Clean characters only
    password = advanced_gen.generate_secure_password(
        length=16, exclude_similar=True, exclude_ambiguous=True
    )
    score, strength, _ = analyzer.analyze_strength(password)
    print(f"   Clean chars:   {password} ({strength}, {score}/100)")
    
    print("\n4. CUSTOM EXCLUSIONS:")
    
    # No vowels
    password = basic_gen.generate_password(length=20, exclude_chars="aeiouAEIOU")
    print(f"   No vowels:     {password}")
    
    # No confusing characters
    password = basic_gen.generate_password(length=20, exclude_chars="0O1lI")
    print(f"   No confusing:  {password}")
    
    print("\n5. STRENGTH COMPARISON:")
    
    weak_examples = [
        ("123456", "Very common"),
        ("password", "Dictionary word"),
        ("Password1", "Basic pattern"),
        ("P@ssw0rd123", "Better but predictable"),
        ("MyS3cur3P@ssw0rd!", "Strong password"),
    ]
    
    for password, description in weak_examples:
        score, strength, feedback = analyzer.analyze_strength(password)
        feedback_summary = feedback[0] if feedback else "No issues"
        print(f"   {password:<20} → {strength:<12} ({score:2d}/100) - {description}")
    
    print("\n6. BATCH GENERATION (5 Strong Passwords):")
    for i in range(5):
        password = advanced_gen.generate_secure_password(length=14, ensure_strength=True)
        score, strength, _ = analyzer.analyze_strength(password)
        print(f"   Password {i+1}: {password} ({strength}, {score}/100)")
    
    print("\n" + "=" * 50)
    print("✅ All passwords generated successfully!")
    print("\nTo run interactive versions:")
    print("   • CLI: python password_generator_cli.py")
    print("   • GUI: python password_generator_gui.py")

if __name__ == "__main__":
    main()
