#!/usr/bin/env python3
"""
Simple Flask Password Generator - Guaranteed to Work
"""

from flask import Flask, render_template, request, jsonify
import random
import string
import secrets

app = Flask(__name__)

def generate_simple_password(length=12, use_lowercase=True, use_uppercase=True, 
                           use_digits=True, use_symbols=True, exclude_chars=""):
    """Generate a simple password."""
    print(f"Generating password: length={length}, lowercase={use_lowercase}, uppercase={use_uppercase}, digits={use_digits}, symbols={use_symbols}")
    
    # Build character set
    chars = ""
    if use_lowercase:
        chars += string.ascii_lowercase
    if use_uppercase:
        chars += string.ascii_uppercase
    if use_digits:
        chars += string.digits
    if use_symbols:
        chars += "!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    # Remove excluded characters
    if exclude_chars:
        chars = ''.join(c for c in chars if c not in exclude_chars)
    
    if not chars:
        raise ValueError("No characters available for password generation")
    
    # Generate password
    password = ''.join(secrets.choice(chars) for _ in range(length))
    print(f"Generated password: {password}")
    return password

def analyze_simple_strength(password):
    """Simple password strength analysis."""
    if not password:
        return 0, "No Password", ["Enter a password"]
    
    score = 0
    feedback = []
    
    # Length scoring
    if len(password) >= 12:
        score += 30
    elif len(password) >= 8:
        score += 20
        feedback.append("Consider using at least 12 characters")
    else:
        score += 10
        feedback.append("Password is too short")
    
    # Character variety
    has_lower = any(c.islower() for c in password)
    has_upper = any(c.isupper() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_symbol = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
    
    variety_score = sum([has_lower, has_upper, has_digit, has_symbol]) * 15
    score += variety_score
    
    if not has_lower:
        feedback.append("Add lowercase letters")
    if not has_upper:
        feedback.append("Add uppercase letters")
    if not has_digit:
        feedback.append("Add numbers")
    if not has_symbol:
        feedback.append("Add symbols")
    
    # Determine strength
    if score >= 80:
        strength = "Very Strong"
    elif score >= 60:
        strength = "Strong"
    elif score >= 40:
        strength = "Medium"
    elif score >= 20:
        strength = "Weak"
    else:
        strength = "Very Weak"
    
    return score, strength, feedback

@app.route('/')
def index():
    """Main page."""
    print("Serving main page")
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate():
    """Generate password."""
    print("Generate endpoint called")
    try:
        data = request.get_json()
        print(f"Received data: {data}")
        
        if not data:
            print("No data received")
            return jsonify({'success': False, 'error': 'No data received'}), 400
        
        # Extract parameters
        length = int(data.get('length', 12))
        use_lowercase = bool(data.get('lowercase', True))
        use_uppercase = bool(data.get('uppercase', True))
        use_digits = bool(data.get('digits', True))
        use_symbols = bool(data.get('symbols', True))
        exclude_chars = str(data.get('exclude', ''))
        
        print(f"Parameters: length={length}, types=[{use_lowercase},{use_uppercase},{use_digits},{use_symbols}], exclude='{exclude_chars}'")
        
        # Validate
        if not any([use_lowercase, use_uppercase, use_digits, use_symbols]):
            return jsonify({'success': False, 'error': 'Select at least one character type'}), 400
        
        if length < 1 or length > 128:
            return jsonify({'success': False, 'error': 'Length must be between 1 and 128'}), 400
        
        # Generate password
        password = generate_simple_password(
            length=length,
            use_lowercase=use_lowercase,
            use_uppercase=use_uppercase,
            use_digits=use_digits,
            use_symbols=use_symbols,
            exclude_chars=exclude_chars
        )
        
        # Analyze strength
        score, strength, feedback = analyze_simple_strength(password)
        
        result = {
            'success': True,
            'password': password,
            'strength': {
                'score': score,
                'level': strength,
                'feedback': feedback
            }
        }
        
        print(f"Returning: {result}")
        return jsonify(result)
        
    except Exception as e:
        error_msg = str(e)
        print(f"Error: {error_msg}")
        return jsonify({'success': False, 'error': error_msg}), 500

@app.route('/enhance', methods=['POST'])
def enhance():
    """Enhance password."""
    print("Enhance endpoint called")
    try:
        data = request.get_json()
        original = data.get('password', '')
        target_length = int(data.get('target_length', 16))
        
        if not original:
            return jsonify({'success': False, 'error': 'No password provided'}), 400
        
        # Simple enhancement: add characters and substitute some
        enhanced = original
        
        # Character substitutions
        subs = {'a': '@', 'e': '3', 'i': '!', 'o': '0', 's': '$'}
        for old, new in subs.items():
            if old in enhanced.lower():
                enhanced = enhanced.replace(old, new, 1)
        
        # Extend length
        while len(enhanced) < target_length:
            enhanced += random.choice('!@#$%^&*123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ')
        
        # Analyze both
        orig_score, orig_strength, orig_feedback = analyze_simple_strength(original)
        enh_score, enh_strength, enh_feedback = analyze_simple_strength(enhanced)
        
        return jsonify({
            'success': True,
            'original': {
                'password': original,
                'score': orig_score,
                'level': orig_strength,
                'feedback': orig_feedback
            },
            'enhanced': {
                'password': enhanced,
                'score': enh_score,
                'level': enh_strength,
                'feedback': enh_feedback
            },
            'improvement': enh_score - orig_score
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/test')
def test():
    """Test endpoint."""
    print("Test endpoint called")
    return jsonify({'status': 'OK', 'message': 'Server is working!'})

@app.route('/test_fix.html')
def test_page():
    """Serve test page."""
    print("Test page requested")
    with open('test_fix.html', 'r') as f:
        return f.read()

if __name__ == '__main__':
    print("🚀 Starting Simple Flask Password Generator...")
    print("🌐 Server will be available at: http://localhost:5000")
    print("🔧 Test endpoint: http://localhost:5000/test")
    try:
        app.run(debug=True, host='127.0.0.1', port=5000)
    except Exception as e:
        print(f"Failed to start server: {e}")
