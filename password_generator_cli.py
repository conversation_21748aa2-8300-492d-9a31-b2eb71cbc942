#!/usr/bin/env python3
"""
Basic Command-Line Password Generator
A simple password generator with user-defined criteria for length and character types.
"""

import random
import string
import sys


class PasswordGenerator:
    """A class to generate random passwords based on user criteria."""
    
    def __init__(self):
        self.lowercase = string.ascii_lowercase
        self.uppercase = string.ascii_uppercase
        self.digits = string.digits
        self.symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    def generate_password(self, length=12, use_lowercase=True, use_uppercase=True, 
                         use_digits=True, use_symbols=True, exclude_chars=""):
        """
        Generate a random password based on specified criteria.
        
        Args:
            length (int): Length of the password (default: 12)
            use_lowercase (bool): Include lowercase letters (default: True)
            use_uppercase (bool): Include uppercase letters (default: True)
            use_digits (bool): Include digits (default: True)
            use_symbols (bool): Include symbols (default: True)
            exclude_chars (str): Characters to exclude from password (default: "")
        
        Returns:
            str: Generated password
        
        Raises:
            ValueError: If no character types are selected or length is invalid
        """
        if length < 1:
            raise ValueError("Password length must be at least 1")
        
        # Build character set based on user preferences
        char_set = ""
        
        if use_lowercase:
            char_set += self.lowercase
        if use_uppercase:
            char_set += self.uppercase
        if use_digits:
            char_set += self.digits
        if use_symbols:
            char_set += self.symbols
        
        if not char_set:
            raise ValueError("At least one character type must be selected")
        
        # Remove excluded characters
        if exclude_chars:
            char_set = ''.join(char for char in char_set if char not in exclude_chars)
        
        if not char_set:
            raise ValueError("No valid characters remaining after exclusions")
        
        # Generate password ensuring at least one character from each selected type
        password = []
        
        # Add at least one character from each selected type
        if use_lowercase and any(c in char_set for c in self.lowercase):
            available_lowercase = [c for c in self.lowercase if c in char_set]
            password.append(random.choice(available_lowercase))
        
        if use_uppercase and any(c in char_set for c in self.uppercase):
            available_uppercase = [c for c in self.uppercase if c in char_set]
            password.append(random.choice(available_uppercase))
        
        if use_digits and any(c in char_set for c in self.digits):
            available_digits = [c for c in self.digits if c in char_set]
            password.append(random.choice(available_digits))
        
        if use_symbols and any(c in char_set for c in self.symbols):
            available_symbols = [c for c in self.symbols if c in char_set]
            password.append(random.choice(available_symbols))
        
        # Fill remaining length with random characters from the full set
        remaining_length = length - len(password)
        for _ in range(remaining_length):
            password.append(random.choice(char_set))
        
        # Shuffle the password to avoid predictable patterns
        random.shuffle(password)
        
        return ''.join(password)


def get_user_input():
    """Get password generation preferences from user input."""
    print("=== Password Generator ===")
    print()
    
    # Get password length
    while True:
        try:
            length = input("Enter password length (default: 12): ").strip()
            if not length:
                length = 12
            else:
                length = int(length)
            
            if length < 1:
                print("Password length must be at least 1. Please try again.")
                continue
            if length > 128:
                print("Password length should not exceed 128 characters. Please try again.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")
    
    # Get character type preferences
    print("\nCharacter types to include:")
    
    use_lowercase = get_yes_no_input("Include lowercase letters (a-z)? [Y/n]: ", default=True)
    use_uppercase = get_yes_no_input("Include uppercase letters (A-Z)? [Y/n]: ", default=True)
    use_digits = get_yes_no_input("Include digits (0-9)? [Y/n]: ", default=True)
    use_symbols = get_yes_no_input("Include symbols (!@#$%^&*...)? [Y/n]: ", default=True)
    
    # Get excluded characters
    exclude_chars = input("\nEnter characters to exclude (or press Enter to skip): ").strip()
    
    return length, use_lowercase, use_uppercase, use_digits, use_symbols, exclude_chars


def get_yes_no_input(prompt, default=True):
    """Get yes/no input from user with default value."""
    while True:
        response = input(prompt).strip().lower()
        if not response:
            return default
        if response in ['y', 'yes', 'true', '1']:
            return True
        elif response in ['n', 'no', 'false', '0']:
            return False
        else:
            print("Please enter 'y' for yes or 'n' for no.")


def main():
    """Main function to run the password generator."""
    try:
        generator = PasswordGenerator()
        
        while True:
            try:
                # Get user preferences
                length, use_lowercase, use_uppercase, use_digits, use_symbols, exclude_chars = get_user_input()
                
                # Generate password
                password = generator.generate_password(
                    length=length,
                    use_lowercase=use_lowercase,
                    use_uppercase=use_uppercase,
                    use_digits=use_digits,
                    use_symbols=use_symbols,
                    exclude_chars=exclude_chars
                )
                
                print(f"\nGenerated Password: {password}")
                print(f"Password Length: {len(password)}")
                
                # Ask if user wants to generate another password
                print()
                if not get_yes_no_input("Generate another password? [Y/n]: ", default=True):
                    break
                    
                print("\n" + "="*50 + "\n")
                
            except ValueError as e:
                print(f"Error: {e}")
                print("Please try again with different settings.\n")
        
        print("\nThank you for using Password Generator!")
        
    except KeyboardInterrupt:
        print("\n\nPassword generation cancelled by user.")
        sys.exit(0)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
