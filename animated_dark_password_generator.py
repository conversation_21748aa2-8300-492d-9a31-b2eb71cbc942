#!/usr/bin/env python3
"""
Animated Dark Theme Password Generator
Beautiful dark UI with smooth animations and visual effects.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import random
import string
import pyperclip
from datetime import datetime
import threading
import time
import math

from password_generator_gui import AdvancedPasswordGenerator, PasswordStrengthAnalyzer
from password_enhancer import PasswordEnhancer


class DarkTheme:
    """Dark theme colors and styling."""
    
    COLORS = {
        # Dark theme palette
        'bg_primary': '#0d1117',      # GitHub dark
        'bg_secondary': '#161b22',    # Darker secondary
        'bg_tertiary': '#21262d',     # Card backgrounds
        'bg_hover': '#30363d',        # Hover states
        
        # Accent colors
        'accent_blue': '#58a6ff',     # Primary blue
        'accent_green': '#3fb950',    # Success green
        'accent_orange': '#f85149',   # Warning/danger
        'accent_purple': '#a5a5ff',   # Enhancement
        'accent_yellow': '#d29922',   # Warning yellow
        
        # Text colors
        'text_primary': '#f0f6fc',    # Main text
        'text_secondary': '#8b949e',  # Secondary text
        'text_muted': '#6e7681',      # Muted text
        
        # Border and effects
        'border': '#30363d',
        'border_focus': '#58a6ff',
        'shadow': '#010409',
    }
    
    FONTS = {
        'title': ('Segoe UI', 20, 'bold'),
        'heading': ('Segoe UI', 14, 'bold'),
        'body': ('Segoe UI', 10),
        'small': ('Segoe UI', 9),
        'mono': ('Consolas', 11),
        'mono_large': ('Consolas', 14, 'bold'),
    }


class AnimatedButton(tk.Canvas):
    """Animated button with hover effects and smooth transitions."""
    
    def __init__(self, parent, text="", command=None, style="primary", 
                 width=150, height=40, **kwargs):
        super().__init__(parent, width=width, height=height, 
                        highlightthickness=0, **kwargs)
        
        self.text = text
        self.command = command
        self.style = style
        self.width = width
        self.height = height
        self.theme = DarkTheme()
        
        # Animation state
        self.hover_scale = 1.0
        self.target_scale = 1.0
        self.animating = False
        
        self.configure(bg=self.theme.COLORS['bg_primary'])
        self.create_button()
        self.bind_events()
    
    def create_button(self):
        """Create button elements."""
        # Button colors
        colors = {
            'primary': self.theme.COLORS['accent_blue'],
            'success': self.theme.COLORS['accent_green'],
            'danger': self.theme.COLORS['accent_orange'],
            'secondary': self.theme.COLORS['bg_tertiary']
        }
        
        self.button_color = colors.get(self.style, colors['primary'])
        self.text_color = self.theme.COLORS['text_primary']
        
        # Create button rectangle
        self.button_rect = self.create_rounded_rect(
            5, 5, self.width-5, self.height-5,
            radius=8, fill=self.button_color, outline=''
        )
        
        # Create text
        self.button_text = self.create_text(
            self.width//2, self.height//2,
            text=self.text, fill=self.text_color,
            font=self.theme.FONTS['body']
        )
    
    def create_rounded_rect(self, x1, y1, x2, y2, radius=10, **kwargs):
        """Create a rounded rectangle."""
        points = []
        for x, y in [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]:
            points.extend([x, y])
        return self.create_polygon(points, smooth=True, **kwargs)
    
    def bind_events(self):
        """Bind mouse events."""
        self.bind('<Enter>', self.on_enter)
        self.bind('<Leave>', self.on_leave)
        self.bind('<Button-1>', self.on_click)
        self.tag_bind(self.button_rect, '<Enter>', self.on_enter)
        self.tag_bind(self.button_rect, '<Leave>', self.on_leave)
        self.tag_bind(self.button_rect, '<Button-1>', self.on_click)
        self.tag_bind(self.button_text, '<Enter>', self.on_enter)
        self.tag_bind(self.button_text, '<Leave>', self.on_leave)
        self.tag_bind(self.button_text, '<Button-1>', self.on_click)
    
    def on_enter(self, event):
        """Handle mouse enter with animation."""
        self.target_scale = 1.05
        self.animate_hover()
    
    def on_leave(self, event):
        """Handle mouse leave with animation."""
        self.target_scale = 1.0
        self.animate_hover()
    
    def on_click(self, event):
        """Handle click with animation."""
        self.animate_click()
        if self.command:
            self.command()
    
    def animate_hover(self):
        """Animate hover effect."""
        if abs(self.hover_scale - self.target_scale) < 0.01:
            self.hover_scale = self.target_scale
            return
        
        # Smooth interpolation
        self.hover_scale += (self.target_scale - self.hover_scale) * 0.2
        
        # Update button appearance
        brightness = 1.0 + (self.hover_scale - 1.0) * 2
        new_color = self.brighten_color(self.button_color, brightness)
        self.itemconfig(self.button_rect, fill=new_color)
        
        # Continue animation
        self.after(16, self.animate_hover)
    
    def animate_click(self):
        """Animate click effect."""
        # Quick scale down and back up
        original_coords = self.coords(self.button_rect)
        
        # Scale down
        self.coords(self.button_rect, 7, 7, self.width-7, self.height-7)
        self.after(50, lambda: self.coords(self.button_rect, *original_coords))
    
    def brighten_color(self, color, factor):
        """Brighten a hex color."""
        # Convert hex to RGB
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        
        # Brighten
        rgb = tuple(min(255, int(c * factor)) for c in rgb)
        
        # Convert back to hex
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"


class AnimatedProgressBar(tk.Canvas):
    """Smooth animated progress bar with glow effects."""
    
    def __init__(self, parent, width=300, height=25, **kwargs):
        super().__init__(parent, width=width, height=height,
                        highlightthickness=0, **kwargs)
        
        self.width = width
        self.height = height
        self.value = 0
        self.target_value = 0
        self.theme = DarkTheme()
        
        self.configure(bg=self.theme.COLORS['bg_primary'])
        self.create_progress_bar()
    
    def create_progress_bar(self):
        """Create progress bar elements."""
        # Background track
        self.track = self.create_rounded_rect(
            2, 2, self.width-2, self.height-2,
            radius=self.height//2,
            fill=self.theme.COLORS['bg_tertiary'],
            outline=self.theme.COLORS['border']
        )
        
        # Progress fill
        self.progress = self.create_rounded_rect(
            2, 2, 2, self.height-2,
            radius=self.height//2,
            fill=self.theme.COLORS['accent_blue'],
            outline=''
        )
        
        # Glow effect (initially hidden)
        self.glow = self.create_rounded_rect(
            0, 0, 0, self.height,
            radius=self.height//2,
            fill='', outline=self.theme.COLORS['accent_blue'],
            width=2
        )
    
    def create_rounded_rect(self, x1, y1, x2, y2, radius=10, **kwargs):
        """Create rounded rectangle."""
        points = []
        for x, y in [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]:
            points.extend([x, y])
        return self.create_polygon(points, smooth=True, **kwargs)
    
    def animate_to(self, target_value):
        """Animate to target value."""
        self.target_value = max(0, min(100, target_value))
        self.animate_step()
    
    def animate_step(self):
        """Single animation step."""
        if abs(self.value - self.target_value) < 0.5:
            self.value = self.target_value
            self.update_progress()
            return
        
        # Smooth easing
        self.value += (self.target_value - self.value) * 0.15
        self.update_progress()
        
        # Continue animation
        self.after(16, self.animate_step)
    
    def update_progress(self):
        """Update progress bar visual."""
        progress_width = (self.width - 4) * (self.value / 100)
        
        # Update progress fill
        self.coords(self.progress, 2, 2, 2 + progress_width, self.height - 2)
        
        # Update glow
        self.coords(self.glow, 0, 0, 4 + progress_width, self.height)
        
        # Color based on value
        if self.value >= 80:
            color = self.theme.COLORS['accent_green']
        elif self.value >= 60:
            color = self.theme.COLORS['accent_blue']
        elif self.value >= 40:
            color = self.theme.COLORS['accent_yellow']
        else:
            color = self.theme.COLORS['accent_orange']
        
        self.itemconfig(self.progress, fill=color)
        self.itemconfig(self.glow, outline=color)


class FloatingParticle:
    """Floating particle for background animation."""
    
    def __init__(self, canvas, x, y):
        self.canvas = canvas
        self.x = x
        self.y = y
        self.dx = random.uniform(-0.5, 0.5)
        self.dy = random.uniform(-0.5, 0.5)
        self.size = random.randint(2, 4)
        self.opacity = random.uniform(0.3, 0.7)
        
        theme = DarkTheme()
        self.color = theme.COLORS['accent_blue']
        
        # Create particle
        self.particle = self.canvas.create_oval(
            x, y, x + self.size, y + self.size,
            fill=self.color, outline='',
            stipple='gray25'
        )
    
    def update(self):
        """Update particle position."""
        self.x += self.dx
        self.y += self.dy
        
        # Wrap around screen
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if self.x < 0:
            self.x = canvas_width
        elif self.x > canvas_width:
            self.x = 0
        
        if self.y < 0:
            self.y = canvas_height
        elif self.y > canvas_height:
            self.y = 0
        
        # Update canvas position
        self.canvas.coords(
            self.particle,
            self.x, self.y,
            self.x + self.size, self.y + self.size
        )


class AnimatedDarkPasswordGenerator:
    """Main application with dark theme and animations."""
    
    def __init__(self, root):
        self.root = root
        self.theme = DarkTheme()
        
        # Initialize components
        self.generator = AdvancedPasswordGenerator()
        self.analyzer = PasswordStrengthAnalyzer()
        self.enhancer = PasswordEnhancer()
        
        # Application state
        self.current_password = tk.StringVar()
        self.reference_password = tk.StringVar()
        self.particles = []
        
        self.setup_window()
        self.create_background()
        self.create_interface()
        self.start_animations()
        
        # Generate initial password
        self.generate_password()
    
    def setup_window(self):
        """Setup main window with dark theme."""
        self.root.title("🔐 Animated Dark Password Generator")
        self.root.geometry("1000x700")
        self.root.configure(bg=self.theme.COLORS['bg_primary'])
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f"1000x700+{x}+{y}")
    
    def create_background(self):
        """Create animated background with particles."""
        self.bg_canvas = tk.Canvas(
            self.root,
            highlightthickness=0,
            bg=self.theme.COLORS['bg_primary']
        )
        self.bg_canvas.place(x=0, y=0, relwidth=1, relheight=1)
        
        # Create floating particles
        for _ in range(15):
            x = random.randint(0, 1000)
            y = random.randint(0, 700)
            particle = FloatingParticle(self.bg_canvas, x, y)
            self.particles.append(particle)
    
    def create_interface(self):
        """Create the main interface."""
        # Main container
        main_frame = tk.Frame(
            self.root,
            bg=self.theme.COLORS['bg_primary'],
            padx=30,
            pady=30
        )
        main_frame.place(relx=0, rely=0, relwidth=1, relheight=1)
        
        # Title with glow effect
        self.create_title(main_frame)
        
        # Content area
        content_frame = tk.Frame(
            main_frame,
            bg=self.theme.COLORS['bg_primary']
        )
        content_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # Left panel - Generator
        self.create_generator_panel(content_frame)
        
        # Right panel - Enhancement
        self.create_enhancement_panel(content_frame)

    def create_title(self, parent):
        """Create animated title."""
        title_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_primary']
        )
        title_frame.pack(fill='x', pady=(0, 20))

        # Main title
        title_label = tk.Label(
            title_frame,
            text="🔐 Animated Dark Password Generator",
            font=self.theme.FONTS['title'],
            bg=self.theme.COLORS['bg_primary'],
            fg=self.theme.COLORS['text_primary']
        )
        title_label.pack()

        # Subtitle
        subtitle_label = tk.Label(
            title_frame,
            text="Beautiful Dark Theme • Smooth Animations • Visual Controls",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_primary'],
            fg=self.theme.COLORS['text_secondary']
        )
        subtitle_label.pack(pady=(5, 0))

    def create_generator_panel(self, parent):
        """Create generator panel with dark theme."""
        # Left side container
        left_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_primary']
        )
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 15))

        # Generator card
        gen_card = self.create_card(left_frame, "🎲 Password Generator")

        # Password length slider
        self.create_length_control(gen_card)

        # Character options
        self.create_character_options(gen_card)

        # Generate button
        self.generate_btn = AnimatedButton(
            gen_card,
            text="🎲 Generate Secure Password",
            command=self.generate_password,
            style="primary",
            width=280, height=50,
            bg=self.theme.COLORS['bg_tertiary']
        )
        self.generate_btn.pack(pady=(20, 10))

        # Password display
        self.create_password_display(gen_card)

        # Strength analysis
        self.create_strength_analysis(gen_card)

    def create_enhancement_panel(self, parent):
        """Create enhancement panel."""
        # Right side container
        right_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_primary']
        )
        right_frame.pack(side='right', fill='both', expand=True, padx=(15, 0))

        # Enhancement card
        enh_card = self.create_card(right_frame, "⚡ Password Enhancement")

        # Input section
        self.create_enhancement_input(enh_card)

        # Enhancement results
        self.create_enhancement_results(enh_card)

    def create_card(self, parent, title):
        """Create a dark themed card."""
        # Card container
        card_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_secondary'],
            relief='flat',
            bd=1,
            highlightbackground=self.theme.COLORS['border'],
            highlightthickness=1
        )
        card_frame.pack(fill='both', expand=True, pady=(0, 10))

        # Title bar
        title_bar = tk.Frame(
            card_frame,
            bg=self.theme.COLORS['accent_blue'],
            height=40
        )
        title_bar.pack(fill='x')
        title_bar.pack_propagate(False)

        title_label = tk.Label(
            title_bar,
            text=title,
            font=self.theme.FONTS['heading'],
            bg=self.theme.COLORS['accent_blue'],
            fg=self.theme.COLORS['text_primary'],
            pady=8
        )
        title_label.pack()

        # Content area
        content_frame = tk.Frame(
            card_frame,
            bg=self.theme.COLORS['bg_secondary'],
            padx=20,
            pady=20
        )
        content_frame.pack(fill='both', expand=True)

        return content_frame

    def create_length_control(self, parent):
        """Create animated length control."""
        length_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_secondary']
        )
        length_frame.pack(fill='x', pady=(0, 20))

        # Label
        tk.Label(
            length_frame,
            text="Password Length:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 10))

        # Slider container
        slider_frame = tk.Frame(
            length_frame,
            bg=self.theme.COLORS['bg_secondary']
        )
        slider_frame.pack(fill='x')
        slider_frame.columnconfigure(0, weight=1)

        # Custom slider
        self.length_var = tk.IntVar(value=16)
        self.length_scale = tk.Scale(
            slider_frame,
            from_=8, to=64,
            variable=self.length_var,
            orient='horizontal',
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_primary'],
            highlightthickness=0,
            troughcolor=self.theme.COLORS['bg_primary'],
            activebackground=self.theme.COLORS['accent_blue'],
            command=self.on_length_change
        )
        self.length_scale.grid(row=0, column=0, sticky='ew', padx=(0, 10))

        # Length display
        self.length_display = tk.Label(
            slider_frame,
            text="16",
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['accent_blue'],
            padx=15,
            pady=8,
            relief='flat'
        )
        self.length_display.grid(row=0, column=1)

    def create_character_options(self, parent):
        """Create character type options."""
        options_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_secondary']
        )
        options_frame.pack(fill='x', pady=(0, 20))

        tk.Label(
            options_frame,
            text="Character Types:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 10))

        # Character options
        self.char_vars = {}
        options = [
            ('lowercase', '🔤 Lowercase (a-z)', True),
            ('uppercase', '🔠 Uppercase (A-Z)', True),
            ('digits', '🔢 Digits (0-9)', True),
            ('symbols', '🔣 Symbols (!@#$...)', True)
        ]

        for key, text, default in options:
            var = tk.BooleanVar(value=default)
            self.char_vars[key] = var

            cb = tk.Checkbutton(
                options_frame,
                text=text,
                variable=var,
                font=self.theme.FONTS['body'],
                bg=self.theme.COLORS['bg_secondary'],
                fg=self.theme.COLORS['text_primary'],
                selectcolor=self.theme.COLORS['accent_blue'],
                activebackground=self.theme.COLORS['bg_secondary'],
                activeforeground=self.theme.COLORS['text_primary'],
                relief='flat',
                command=self.on_settings_change
            )
            cb.pack(anchor='w', pady=3)

    def create_password_display(self, parent):
        """Create password display with animations."""
        display_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_secondary']
        )
        display_frame.pack(fill='x', pady=(0, 20))

        tk.Label(
            display_frame,
            text="Generated Password:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 10))

        # Password entry
        self.password_entry = tk.Entry(
            display_frame,
            textvariable=self.current_password,
            font=self.theme.FONTS['mono_large'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['accent_green'],
            insertbackground=self.theme.COLORS['accent_blue'],
            relief='flat',
            bd=10,
            state='readonly',
            readonlybackground=self.theme.COLORS['bg_tertiary']
        )
        self.password_entry.pack(fill='x', ipady=8)

        # Action buttons
        action_frame = tk.Frame(
            display_frame,
            bg=self.theme.COLORS['bg_secondary']
        )
        action_frame.pack(fill='x', pady=(15, 0))
        action_frame.columnconfigure(0, weight=1)
        action_frame.columnconfigure(1, weight=1)

        copy_btn = AnimatedButton(
            action_frame,
            text="📋 Copy",
            command=self.copy_password,
            style="success",
            width=130, height=40,
            bg=self.theme.COLORS['bg_secondary']
        )
        copy_btn.grid(row=0, column=0, padx=(0, 10), sticky='ew')

        save_btn = AnimatedButton(
            action_frame,
            text="💾 Save",
            command=self.save_password,
            style="secondary",
            width=130, height=40,
            bg=self.theme.COLORS['bg_secondary']
        )
        save_btn.grid(row=0, column=1, padx=(10, 0), sticky='ew')

    def create_strength_analysis(self, parent):
        """Create animated strength analysis."""
        strength_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_secondary']
        )
        strength_frame.pack(fill='x')

        tk.Label(
            strength_frame,
            text="Password Strength:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 10))

        # Strength meter
        meter_frame = tk.Frame(
            strength_frame,
            bg=self.theme.COLORS['bg_secondary']
        )
        meter_frame.pack(fill='x', pady=(0, 10))
        meter_frame.columnconfigure(0, weight=1)

        self.strength_progress = AnimatedProgressBar(
            meter_frame,
            width=250, height=20,
            bg=self.theme.COLORS['bg_secondary']
        )
        self.strength_progress.grid(row=0, column=0, sticky='ew', padx=(0, 10))

        self.strength_var = tk.StringVar(value="Medium")
        self.strength_label = tk.Label(
            meter_frame,
            textvariable=self.strength_var,
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        )
        self.strength_label.grid(row=0, column=1)

        # Feedback area
        self.feedback_text = tk.Text(
            strength_frame,
            height=4,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_secondary'],
            relief='flat',
            bd=8,
            wrap='word',
            state='disabled'
        )
        self.feedback_text.pack(fill='x')

    def create_enhancement_input(self, parent):
        """Create enhancement input section."""
        input_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_secondary']
        )
        input_frame.pack(fill='x', pady=(0, 20))

        tk.Label(
            input_frame,
            text="Enter Your Current Password:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 10))

        # Password input
        self.ref_password_entry = tk.Entry(
            input_frame,
            textvariable=self.reference_password,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_primary'],
            insertbackground=self.theme.COLORS['accent_blue'],
            relief='flat',
            bd=8,
            show='*'
        )
        self.ref_password_entry.pack(fill='x', ipady=8)

        # Show/hide toggle
        toggle_frame = tk.Frame(
            input_frame,
            bg=self.theme.COLORS['bg_secondary']
        )
        toggle_frame.pack(fill='x', pady=(10, 0))

        self.show_password_var = tk.BooleanVar()
        show_cb = tk.Checkbutton(
            toggle_frame,
            text="👁️ Show password",
            variable=self.show_password_var,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_secondary'],
            selectcolor=self.theme.COLORS['accent_blue'],
            activebackground=self.theme.COLORS['bg_secondary'],
            activeforeground=self.theme.COLORS['text_secondary'],
            relief='flat',
            command=self.toggle_password_visibility
        )
        show_cb.pack(anchor='w')

        # Enhancement options
        options_frame = tk.Frame(
            input_frame,
            bg=self.theme.COLORS['bg_secondary']
        )
        options_frame.pack(fill='x', pady=(20, 0))

        tk.Label(
            options_frame,
            text="Enhancement Options:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 10))

        # Target length
        length_frame = tk.Frame(
            options_frame,
            bg=self.theme.COLORS['bg_secondary']
        )
        length_frame.pack(fill='x', pady=(0, 10))
        length_frame.columnconfigure(0, weight=1)

        tk.Label(
            length_frame,
            text="Target Length:",
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_secondary']
        ).grid(row=0, column=0, sticky='w')

        self.target_length_var = tk.IntVar(value=16)
        target_scale = tk.Scale(
            length_frame,
            from_=8, to=32,
            variable=self.target_length_var,
            orient='horizontal',
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_primary'],
            highlightthickness=0,
            troughcolor=self.theme.COLORS['bg_primary'],
            activebackground=self.theme.COLORS['accent_purple']
        )
        target_scale.grid(row=1, column=0, sticky='ew', pady=(5, 0))

        # Readability option
        self.maintain_readability_var = tk.BooleanVar(value=True)
        readability_cb = tk.Checkbutton(
            options_frame,
            text="🔤 Maintain readability",
            variable=self.maintain_readability_var,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_secondary'],
            selectcolor=self.theme.COLORS['accent_purple'],
            activebackground=self.theme.COLORS['bg_secondary'],
            activeforeground=self.theme.COLORS['text_secondary'],
            relief='flat'
        )
        readability_cb.pack(anchor='w', pady=(10, 0))

        # Enhance button
        enhance_btn = AnimatedButton(
            input_frame,
            text="⚡ Enhance Password",
            command=self.enhance_password,
            style="primary",
            width=280, height=50,
            bg=self.theme.COLORS['bg_secondary']
        )
        enhance_btn.pack(pady=(20, 0))

    def create_enhancement_results(self, parent):
        """Create enhancement results display."""
        results_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_secondary']
        )
        results_frame.pack(fill='both', expand=True)

        tk.Label(
            results_frame,
            text="Enhancement Results:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 10))

        # Before/After comparison
        comparison_frame = tk.Frame(
            results_frame,
            bg=self.theme.COLORS['bg_secondary']
        )
        comparison_frame.pack(fill='x', pady=(0, 15))
        comparison_frame.columnconfigure(0, weight=1)
        comparison_frame.columnconfigure(1, weight=1)

        # Before
        before_frame = tk.Frame(
            comparison_frame,
            bg=self.theme.COLORS['bg_secondary']
        )
        before_frame.grid(row=0, column=0, sticky='ew', padx=(0, 5))

        tk.Label(
            before_frame,
            text="Before:",
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['accent_orange']
        ).pack(anchor='w')

        self.before_password_var = tk.StringVar()
        self.before_display = tk.Entry(
            before_frame,
            textvariable=self.before_password_var,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['accent_orange'],
            state='readonly',
            relief='flat',
            bd=5,
            readonlybackground=self.theme.COLORS['bg_tertiary']
        )
        self.before_display.pack(fill='x', pady=(5, 0))

        # After
        after_frame = tk.Frame(
            comparison_frame,
            bg=self.theme.COLORS['bg_secondary']
        )
        after_frame.grid(row=0, column=1, sticky='ew', padx=(5, 0))

        tk.Label(
            after_frame,
            text="After:",
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['accent_green']
        ).pack(anchor='w')

        self.after_password_var = tk.StringVar()
        self.after_display = tk.Entry(
            after_frame,
            textvariable=self.after_password_var,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['accent_green'],
            state='readonly',
            relief='flat',
            bd=5,
            readonlybackground=self.theme.COLORS['bg_tertiary']
        )
        self.after_display.pack(fill='x', pady=(5, 0))

        # Enhancement details
        self.enhancement_details = tk.Text(
            results_frame,
            height=6,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_secondary'],
            relief='flat',
            bd=8,
            wrap='word',
            state='disabled'
        )
        self.enhancement_details.pack(fill='both', expand=True, pady=(15, 0))

    def start_animations(self):
        """Start background animations."""
        self.animate_particles()
        self.animate_title_glow()

    def animate_particles(self):
        """Animate floating particles."""
        for particle in self.particles:
            particle.update()

        # Schedule next frame
        self.root.after(50, self.animate_particles)

    def animate_title_glow(self):
        """Animate title glow effect."""
        # This could add a pulsing glow effect
        self.root.after(2000, self.animate_title_glow)

    # Core functionality methods
    def on_length_change(self, value):
        """Handle length change."""
        length = int(float(value))
        self.length_display.config(text=str(length))
        self.on_settings_change()

    def on_settings_change(self):
        """Handle settings change."""
        if hasattr(self, '_settings_timer'):
            self.root.after_cancel(self._settings_timer)
        self._settings_timer = self.root.after(300, self.generate_password)

    def generate_password(self):
        """Generate new password with animations."""
        try:
            password = self.generator.generate_secure_password(
                length=self.length_var.get(),
                use_lowercase=self.char_vars['lowercase'].get(),
                use_uppercase=self.char_vars['uppercase'].get(),
                use_digits=self.char_vars['digits'].get(),
                use_symbols=self.char_vars['symbols'].get(),
                exclude_chars="",
                exclude_similar=False,
                exclude_ambiguous=False,
                ensure_strength=True
            )

            self.current_password.set(password)
            self.analyze_password_strength(password)
            self.animate_password_generation()

        except ValueError as e:
            messagebox.showerror("Generation Error", str(e))

    def analyze_password_strength(self, password):
        """Analyze password strength with animations."""
        score, strength, feedback = self.analyzer.analyze_strength(password)

        # Update strength display with animation
        self.strength_var.set(f"{strength} ({score}/100)")
        self.strength_progress.animate_to(score)

        # Update feedback
        self.feedback_text.config(state='normal')
        self.feedback_text.delete(1.0, tk.END)

        if feedback:
            self.feedback_text.insert(tk.END, "💡 Security Suggestions:\n\n")
            for i, suggestion in enumerate(feedback[:3], 1):
                self.feedback_text.insert(tk.END, f"{i}. {suggestion}\n")
        else:
            self.feedback_text.insert(tk.END, "✅ Excellent! This password is very secure.")

        self.feedback_text.config(state='disabled')

    def animate_password_generation(self):
        """Animate password generation."""
        # Flash effect
        original_bg = self.password_entry.cget('readonlybackground')
        self.password_entry.config(readonlybackground=self.theme.COLORS['accent_blue'])
        self.root.after(100, lambda: self.password_entry.config(readonlybackground=original_bg))

    def copy_password(self):
        """Copy password with animation."""
        password = self.current_password.get()
        if password:
            try:
                pyperclip.copy(password)
                self.show_notification("Password copied to clipboard! 📋", "success")
            except Exception as e:
                messagebox.showerror("Clipboard Error", f"Failed to copy: {e}")
        else:
            messagebox.showwarning("No Password", "No password to copy!")

    def save_password(self):
        """Save password with animation."""
        password = self.current_password.get()
        if password:
            self.show_notification("Password saved! 💾", "success")
        else:
            messagebox.showwarning("No Password", "No password to save!")

    def toggle_password_visibility(self):
        """Toggle password visibility."""
        if self.show_password_var.get():
            self.ref_password_entry.config(show='')
        else:
            self.ref_password_entry.config(show='*')

    def enhance_password(self):
        """Enhance password with animations."""
        ref_password = self.reference_password.get().strip()
        if not ref_password:
            messagebox.showwarning("No Password", "Please enter a password to enhance!")
            return

        try:
            result = self.enhancer.enhance_password(
                ref_password,
                target_length=self.target_length_var.get(),
                maintain_readability=self.maintain_readability_var.get()
            )

            # Show results with animation
            self.show_enhancement_results(result)

        except Exception as e:
            messagebox.showerror("Enhancement Error", f"Failed to enhance: {e}")

    def show_enhancement_results(self, result):
        """Show enhancement results with animations."""
        # Update displays
        self.before_password_var.set(result['original'])
        self.after_password_var.set(result['enhanced'])

        # Update details
        self.enhancement_details.config(state='normal')
        self.enhancement_details.delete(1.0, tk.END)

        orig_analysis = result['original_analysis']
        enh_analysis = result['enhanced_analysis']

        details = f"🚀 Enhancement Complete!\n\n"
        details += f"📈 Improvement: +{result['improvement']} points\n"
        details += f"🔧 Strategies: {', '.join(result['strategies_used'])}\n"
        details += f"💪 New Strength: {enh_analysis['strength']} ({enh_analysis['score']}/100)"

        self.enhancement_details.insert(tk.END, details)
        self.enhancement_details.config(state='disabled')

        self.show_notification("Password enhanced successfully! ⚡", "success")
        self.animate_enhancement_success()

    def animate_enhancement_success(self):
        """Animate enhancement success."""
        # Flash effect on after display
        original_bg = self.after_display.cget('readonlybackground')
        self.after_display.config(readonlybackground=self.theme.COLORS['accent_green'])
        self.root.after(200, lambda: self.after_display.config(readonlybackground=original_bg))

    def show_notification(self, message, type="info"):
        """Show animated notification."""
        # Create notification window
        notification = tk.Toplevel(self.root)
        notification.wm_overrideredirect(True)
        notification.attributes('-topmost', True)

        # Position at top-right
        x = self.root.winfo_x() + self.root.winfo_width() - 320
        y = self.root.winfo_y() + 50
        notification.geometry(f"300x70+{x}+{y}")

        # Style based on type
        if type == "success":
            bg_color = self.theme.COLORS['accent_green']
        else:
            bg_color = self.theme.COLORS['accent_blue']

        notification.configure(bg=bg_color)

        # Message
        message_label = tk.Label(
            notification,
            text=message,
            font=self.theme.FONTS['body'],
            bg=bg_color,
            fg=self.theme.COLORS['text_primary'],
            padx=20,
            pady=20
        )
        message_label.pack(fill='both', expand=True)

        # Auto-hide
        notification.after(2500, notification.destroy)


def main():
    """Main function to run the animated dark password generator."""
    try:
        root = tk.Tk()
        app = AnimatedDarkPasswordGenerator(root)
        root.mainloop()
    except ImportError as e:
        if "pyperclip" in str(e):
            print("Error: pyperclip module is required for clipboard functionality.")
            print("Install it using: pip install pyperclip")
        else:
            print(f"Import error: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()
