# Random Password Generator

A comprehensive password generation tool with both command-line and graphical user interfaces. This project demonstrates key programming concepts including randomization, user input validation, GUI design, security rules implementation, and clipboard integration.

## Features

### Command-Line Version (`password_generator_cli.py`)
- **Interactive CLI Interface**: User-friendly command-line prompts
- **Customizable Length**: Password length from 1 to 128 characters
- **Character Type Selection**: Choose from lowercase, uppercase, digits, and symbols
- **Character Exclusion**: Exclude specific characters from generation
- **Input Validation**: Robust validation for all user inputs
- **Multiple Password Generation**: Generate multiple passwords in one session

### GUI Version (`password_generator_gui.py`)
- **Modern Tkinter Interface**: Intuitive graphical user interface
- **Advanced Security Options**: 
  - Exclude visually similar characters (il1Lo0O)
  - Exclude ambiguous characters ({}[]()...)
  - Ensure minimum strength requirements
- **Real-time Strength Analysis**: Password strength scoring and feedback
- **Clipboard Integration**: One-click copy to clipboard
- **Password History**: Save and reuse previously generated passwords
- **Visual Strength Meter**: Color-coded strength indicator with progress bar
- **Detailed Security Feedback**: Specific suggestions for password improvement

## Installation

### Prerequisites
- Python 3.6 or higher
- pip (Python package installer)

### Setup
1. Clone or download this repository
2. Navigate to the project directory
3. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Dependencies
- **pyperclip**: For clipboard functionality in the GUI version
- **tkinter**: Built-in with Python (for GUI)

## Usage

### Command-Line Version
Run the CLI password generator:
```bash
python password_generator_cli.py
```

Follow the interactive prompts to:
1. Set password length (default: 12)
2. Choose character types to include
3. Specify characters to exclude (optional)
4. Generate multiple passwords as needed

### GUI Version
Launch the graphical interface:
```bash
python password_generator_gui.py
```

#### GUI Features:
1. **Password Length**: Use the slider to set length (4-64 characters)
2. **Character Types**: Check/uncheck boxes for different character types
3. **Advanced Options**:
   - Exclude similar characters for better readability
   - Exclude ambiguous characters for system compatibility
   - Ensure strong password generation
4. **Custom Exclusions**: Enter specific characters to exclude
5. **Generate**: Click to create a new password
6. **Actions**: Copy to clipboard, save to history, or clear
7. **Strength Analysis**: View detailed security assessment
8. **History**: Access previously generated passwords

## Security Features

### Password Strength Analysis
The GUI version includes a comprehensive strength analyzer that evaluates:
- **Length**: Longer passwords receive higher scores
- **Character Variety**: Mix of uppercase, lowercase, numbers, and symbols
- **Pattern Detection**: Identifies and penalizes common patterns
- **Common Words**: Detects and warns about dictionary words
- **Sequential Characters**: Flags sequential numbers or letters

### Strength Scoring
- **Very Strong (80-100)**: Excellent security
- **Strong (60-79)**: Good security with minor improvements possible
- **Medium (40-59)**: Adequate but could be stronger
- **Weak (20-39)**: Poor security, needs improvement
- **Very Weak (0-19)**: Unacceptable security level

### Security Best Practices Implemented
- Guaranteed character type representation
- Cryptographically secure randomization
- Pattern avoidance algorithms
- Configurable exclusion options
- Strength-based regeneration

## Code Structure

### Core Classes

#### `PasswordGenerator` (CLI)
- Basic password generation with user-defined criteria
- Input validation and error handling
- Character set management

#### `AdvancedPasswordGenerator` (GUI)
- Enhanced security features
- Advanced exclusion options
- Strength-based generation

#### `PasswordStrengthAnalyzer` (GUI)
- Comprehensive strength analysis
- Security feedback generation
- Pattern detection algorithms

#### `PasswordGeneratorGUI` (GUI)
- Complete Tkinter interface
- Event handling and user interactions
- History management and clipboard integration

## Key Programming Concepts Demonstrated

### 1. Randomization
- Secure random character selection
- Password shuffling to avoid patterns
- Cryptographically appropriate randomness

### 2. User Input Validation
- Type checking and range validation
- Error handling with user-friendly messages
- Default value management

### 3. Character Set Handling
- Dynamic character set construction
- Exclusion filtering
- Character type guarantees

### 4. GUI Design (Advanced Version)
- Responsive layout with proper widget arrangement
- Event-driven programming
- User experience optimization

### 5. Security Rules Implementation
- Password strength algorithms
- Pattern detection and avoidance
- Security best practices enforcement

### 6. Clipboard Integration
- Cross-platform clipboard access
- Error handling for clipboard operations
- User feedback for successful operations

## Customization Options

### CLI Version
- Password length (1-128 characters)
- Character type inclusion/exclusion
- Custom character exclusions
- Multiple generation sessions

### GUI Version
- All CLI features plus:
- Visual similar character exclusion
- Ambiguous character filtering
- Strength requirement enforcement
- History management
- Real-time strength feedback

## Error Handling

Both versions include comprehensive error handling for:
- Invalid input values
- Empty character sets
- Clipboard access failures
- System compatibility issues

## Future Enhancements

Potential improvements for advanced users:
- Password policy compliance checking
- Export/import functionality for password history
- Batch password generation
- Integration with password managers
- Custom character set definitions
- Pronunciation guides for generated passwords

## Contributing

This project is designed for educational purposes. Feel free to:
- Add new features
- Improve the user interface
- Enhance security algorithms
- Add unit tests
- Optimize performance

## License

This project is provided for educational purposes. Feel free to use, modify, and distribute as needed.

## Troubleshooting

### Common Issues

1. **"pyperclip module not found"**
   - Solution: Install with `pip install pyperclip`

2. **GUI doesn't start**
   - Ensure tkinter is available (usually built-in with Python)
   - Try: `python -m tkinter` to test tkinter installation

3. **Clipboard functionality not working**
   - Ensure pyperclip is installed and compatible with your system
   - Some Linux systems may require additional packages

4. **Password appears weak despite settings**
   - Try increasing length or enabling more character types
   - Disable "ensure strength" temporarily if needed

For additional help, check the error messages and ensure all dependencies are properly installed.
