<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Password Generator Pro</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .card h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .slider {
            flex: 1;
            height: 8px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        .length-display {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            min-width: 50px;
            text-align: center;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .exclude-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .exclude-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .password-display {
            background: #f8f9fa;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
            word-break: break-all;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .password-display.generated {
            background: #e8f5e8;
            border-color: #28a745;
            color: #155724;
        }

        .copy-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .strength-meter {
            margin-bottom: 20px;
        }

        .strength-bar {
            width: 100%;
            height: 20px;
            background: #ddd;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .strength-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease, background-color 0.5s ease;
            width: 0%;
        }

        .strength-fill.very-weak { background: #dc3545; }
        .strength-fill.weak { background: #fd7e14; }
        .strength-fill.medium { background: #ffc107; }
        .strength-fill.strong { background: #20c997; }
        .strength-fill.very-strong { background: #28a745; }

        .strength-text {
            text-align: center;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .feedback {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .feedback ul {
            margin-left: 20px;
        }

        .feedback li {
            margin-bottom: 5px;
        }

        .enhancement-section {
            grid-column: 1 / -1;
        }

        .enhancement-results {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .comparison-item h4 {
            margin-bottom: 10px;
        }

        .comparison-item .password {
            font-family: 'Courier New', monospace;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 2px solid #ddd;
            word-break: break-all;
        }

        .comparison-item.before .password {
            border-color: #dc3545;
            color: #dc3545;
        }

        .comparison-item.after .password {
            border-color: #28a745;
            color: #28a745;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .checkbox-group {
                grid-template-columns: 1fr;
            }
            
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> Password Generator Pro</h1>
            <p>Generate secure passwords with advanced customization options</p>
        </div>

        <div class="main-content">
            <!-- Password Generator -->
            <div class="card">
                <h2><i class="fas fa-dice"></i> Generate Password</h2>
                
                <div class="form-group">
                    <label for="length">Password Length:</label>
                    <div class="slider-container">
                        <input type="range" id="length" class="slider" min="4" max="64" value="12">
                        <div class="length-display" id="lengthDisplay">12</div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Character Types:</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="lowercase" checked>
                            <label for="lowercase">🔤 Lowercase (a-z)</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="uppercase" checked>
                            <label for="uppercase">🔠 Uppercase (A-Z)</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="digits" checked>
                            <label for="digits">🔢 Digits (0-9)</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="symbols" checked>
                            <label for="symbols">🔣 Symbols (!@#$...)</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="exclude">Exclude Characters:</label>
                    <input type="text" id="exclude" class="exclude-input" placeholder="Enter characters to exclude...">
                </div>

                <button class="btn" onclick="generatePassword()">
                    <i class="fas fa-magic"></i> Generate Password
                </button>

                <button class="btn btn-secondary" onclick="testConnection()" style="margin-top: 10px;">
                    <i class="fas fa-wifi"></i> Test Connection
                </button>
            </div>

            <!-- Password Display & Analysis -->
            <div class="card">
                <h2><i class="fas fa-key"></i> Generated Password</h2>
                
                <div class="password-display" id="passwordDisplay">
                    Click "Generate Password" to create a secure password
                    <button class="copy-btn" id="copyBtn" onclick="copyPassword()" style="display: none;">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </div>

                <div class="strength-meter">
                    <div class="strength-text" id="strengthText">Password Strength: Not Generated</div>
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                </div>

                <div class="feedback" id="feedback">
                    <strong>Security Tips:</strong>
                    <ul>
                        <li>Use at least 12 characters</li>
                        <li>Include uppercase, lowercase, numbers, and symbols</li>
                        <li>Avoid common words and patterns</li>
                        <li>Don't reuse passwords across sites</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Password Enhancement -->
        <div class="card enhancement-section">
            <h2><i class="fas fa-bolt"></i> Password Enhancement</h2>
            <p style="margin-bottom: 20px; color: #666;">Enter your current weak password to transform it into a strong one:</p>
            
            <div style="display: grid; grid-template-columns: 2fr 1fr 1fr; gap: 15px; align-items: end;">
                <div class="form-group" style="margin-bottom: 0;">
                    <label for="weakPassword">Current Password:</label>
                    <input type="password" id="weakPassword" class="exclude-input" placeholder="Enter your weak password...">
                </div>
                <div class="form-group" style="margin-bottom: 0;">
                    <label for="targetLength">Target Length:</label>
                    <input type="number" id="targetLength" class="exclude-input" value="16" min="8" max="32">
                </div>
                <button class="btn btn-secondary" onclick="enhancePassword()">
                    <i class="fas fa-magic"></i> Enhance
                </button>
            </div>

            <div class="enhancement-results" id="enhancementResults">
                <h3>Enhancement Results:</h3>
                <div class="comparison">
                    <div class="comparison-item before">
                        <h4><i class="fas fa-times-circle" style="color: #dc3545;"></i> Before:</h4>
                        <div class="password" id="beforePassword"></div>
                        <div id="beforeStrength"></div>
                    </div>
                    <div class="comparison-item after">
                        <h4><i class="fas fa-check-circle" style="color: #28a745;"></i> After:</h4>
                        <div class="password" id="afterPassword"></div>
                        <div id="afterStrength"></div>
                    </div>
                </div>
                <div id="improvementDetails"></div>
            </div>
        </div>
    </div>

    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div>Processing...</div>
    </div>

    <script>
        // Update length display
        document.getElementById('length').addEventListener('input', function() {
            document.getElementById('lengthDisplay').textContent = this.value;
        });

        // Auto-generate on settings change
        document.querySelectorAll('#length, #lowercase, #uppercase, #digits, #symbols, #exclude').forEach(element => {
            element.addEventListener('input', debounce(generatePassword, 500));
        });

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        async function generatePassword() {
            // Validate that at least one character type is selected
            const lowercase = document.getElementById('lowercase').checked;
            const uppercase = document.getElementById('uppercase').checked;
            const digits = document.getElementById('digits').checked;
            const symbols = document.getElementById('symbols').checked;

            if (!lowercase && !uppercase && !digits && !symbols) {
                showNotification('Please select at least one character type!', 'error');
                return;
            }

            const data = {
                length: parseInt(document.getElementById('length').value),
                lowercase: lowercase,
                uppercase: uppercase,
                digits: digits,
                symbols: symbols,
                exclude: document.getElementById('exclude').value
            };

            console.log('Generating password with data:', data);

            try {
                const response = await fetch('/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                console.log('Response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('Response data:', result);

                if (result.success) {
                    displayPassword(result.password, result.strength);
                    showNotification('Password generated successfully!', 'success');
                } else {
                    showNotification(result.error || 'Unknown error occurred', 'error');
                    console.error('Generation error:', result.error);
                }
            } catch (error) {
                console.error('Fetch error:', error);
                showNotification('Failed to generate password: ' + error.message, 'error');

                // Fallback: generate a simple password client-side
                generateFallbackPassword();
            }
        }

        function generateFallbackPassword() {
            console.log('Using fallback password generation');
            const length = parseInt(document.getElementById('length').value) || 12;
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
            let password = '';
            for (let i = 0; i < length; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }

            const fallbackStrength = {
                score: 75,
                level: 'Strong',
                feedback: ['Generated using fallback method']
            };

            displayPassword(password, fallbackStrength);
            showNotification('Password generated (fallback mode)', 'success');
        }

        function displayPassword(password, strength) {
            const display = document.getElementById('passwordDisplay');
            if (display) {
                display.textContent = password;
                display.className = 'password-display generated';
            }

            const copyBtn = document.getElementById('copyBtn');
            if (copyBtn) {
                copyBtn.style.display = 'block';
            }

            updateStrengthMeter(strength);
        }

        function updateStrengthMeter(strength) {
            const fill = document.getElementById('strengthFill');
            const text = document.getElementById('strengthText');
            const feedback = document.getElementById('feedback');

            if (fill) {
                fill.style.width = strength.score + '%';
                fill.className = 'strength-fill ' + strength.level.toLowerCase().replace(' ', '-');
            }

            if (text) {
                text.textContent = `Password Strength: ${strength.level} (${strength.score}/100)`;
            }

            if (feedback) {
                if (strength.feedback && strength.feedback.length > 0) {
                    feedback.innerHTML = '<strong>Suggestions:</strong><ul>' +
                        strength.feedback.map(item => `<li>${item}</li>`).join('') + '</ul>';
                } else {
                    feedback.innerHTML = '<strong>Excellent!</strong> This password meets all security criteria.';
                }
            }
        }

        async function copyPassword() {
            const passwordDisplay = document.getElementById('passwordDisplay');
            if (!passwordDisplay) {
                showNotification('No password to copy', 'error');
                return;
            }

            const password = passwordDisplay.textContent;
            if (!password || password === 'Click "Generate Password" to create a secure password') {
                showNotification('No password generated yet', 'error');
                return;
            }

            try {
                await navigator.clipboard.writeText(password);
                showNotification('Password copied to clipboard!', 'success');
            } catch (error) {
                // Fallback for older browsers
                try {
                    const textArea = document.createElement('textarea');
                    textArea.value = password;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showNotification('Password copied to clipboard!', 'success');
                } catch (fallbackError) {
                    showNotification('Failed to copy password', 'error');
                }
            }
        }

        async function enhancePassword() {
            const weakPassword = document.getElementById('weakPassword').value;
            const targetLength = parseInt(document.getElementById('targetLength').value);
            
            if (!weakPassword) {
                showNotification('Please enter a password to enhance', 'error');
                return;
            }

            document.getElementById('loading').style.display = 'block';

            try {
                const response = await fetch('/enhance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        password: weakPassword,
                        target_length: targetLength
                    })
                });

                const result = await response.json();
                document.getElementById('loading').style.display = 'none';

                if (result.success) {
                    showEnhancementResults(result);
                } else {
                    showNotification(result.error, 'error');
                }
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                showNotification('Failed to enhance password', 'error');
            }
        }

        function showEnhancementResults(result) {
            const beforePassword = document.getElementById('beforePassword');
            const afterPassword = document.getElementById('afterPassword');
            const beforeStrength = document.getElementById('beforeStrength');
            const afterStrength = document.getElementById('afterStrength');
            const improvementDetails = document.getElementById('improvementDetails');
            const enhancementResults = document.getElementById('enhancementResults');

            if (beforePassword) beforePassword.textContent = result.original.password;
            if (afterPassword) afterPassword.textContent = result.enhanced.password;

            if (beforeStrength) {
                beforeStrength.textContent = `${result.original.level} (${result.original.score}/100)`;
            }
            if (afterStrength) {
                afterStrength.textContent = `${result.enhanced.level} (${result.enhanced.score}/100)`;
            }

            if (improvementDetails) {
                improvementDetails.innerHTML =
                    `<strong>Improvement:</strong> +${result.improvement} points<br>` +
                    `<strong>Enhancement successful!</strong> Your password is now much more secure.`;
            }

            if (enhancementResults) {
                enhancementResults.style.display = 'block';
            }

            showNotification('Password enhanced successfully!', 'success');
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        async function testConnection() {
            try {
                showNotification('Testing connection...', 'info');
                const response = await fetch('/', {
                    method: 'GET'
                });

                if (response.ok) {
                    showNotification('✅ Connection successful! Server is running.', 'success');
                    console.log('Server connection test passed');
                } else {
                    showNotification('❌ Server responded with error: ' + response.status, 'error');
                }
            } catch (error) {
                showNotification('❌ Cannot connect to server: ' + error.message, 'error');
                console.error('Connection test failed:', error);
            }
        }

        // Generate initial password after page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, generating initial password...');
            setTimeout(generatePassword, 500); // Small delay to ensure all elements are ready
        });

        // Fallback: try to generate password immediately
        try {
            generatePassword();
        } catch (error) {
            console.log('Initial generation failed, will retry after DOM load:', error);
        }
    </script>
</body>
</html>
