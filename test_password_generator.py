#!/usr/bin/env python3
"""
Unit tests for the Password Generator project.
Tests both CLI and GUI password generation functionality.
"""

import unittest
import string
import re
from password_generator_cli import PasswordGenerator
from password_generator_gui import AdvancedPasswordGenerator, PasswordStrengthAnalyzer


class TestPasswordGenerator(unittest.TestCase):
    """Test cases for the basic CLI password generator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.generator = PasswordGenerator()
    
    def test_default_password_generation(self):
        """Test password generation with default settings."""
        password = self.generator.generate_password()
        self.assertEqual(len(password), 12)  # Default length
        self.assertTrue(any(c.islower() for c in password))  # Has lowercase
        self.assertTrue(any(c.isupper() for c in password))  # Has uppercase
        self.assertTrue(any(c.isdigit() for c in password))  # Has digits
        self.assertTrue(any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password))  # Has symbols
    
    def test_custom_length(self):
        """Test password generation with custom lengths."""
        # Test lengths that allow for character type guarantees
        for length in [4, 8, 16, 32, 64]:  # Start from 4 to ensure all character types can be included
            password = self.generator.generate_password(length=length)
            self.assertEqual(len(password), length)
    
    def test_character_type_selection(self):
        """Test password generation with specific character types."""
        # Only lowercase
        password = self.generator.generate_password(
            length=20, use_lowercase=True, use_uppercase=False, 
            use_digits=False, use_symbols=False
        )
        self.assertTrue(all(c.islower() for c in password))
        
        # Only uppercase
        password = self.generator.generate_password(
            length=20, use_lowercase=False, use_uppercase=True, 
            use_digits=False, use_symbols=False
        )
        self.assertTrue(all(c.isupper() for c in password))
        
        # Only digits
        password = self.generator.generate_password(
            length=20, use_lowercase=False, use_uppercase=False, 
            use_digits=True, use_symbols=False
        )
        self.assertTrue(all(c.isdigit() for c in password))
    
    def test_character_exclusion(self):
        """Test password generation with character exclusions."""
        exclude_chars = "aeiou"
        password = self.generator.generate_password(
            length=50, exclude_chars=exclude_chars
        )
        for char in exclude_chars:
            self.assertNotIn(char, password)
    
    def test_error_conditions(self):
        """Test error handling for invalid inputs."""
        # Invalid length
        with self.assertRaises(ValueError):
            self.generator.generate_password(length=0)
        
        with self.assertRaises(ValueError):
            self.generator.generate_password(length=-1)
        
        # No character types selected
        with self.assertRaises(ValueError):
            self.generator.generate_password(
                use_lowercase=False, use_uppercase=False, 
                use_digits=False, use_symbols=False
            )
        
        # All characters excluded
        with self.assertRaises(ValueError):
            all_chars = string.ascii_letters + string.digits + "!@#$%^&*()_+-=[]{}|;:,.<>?"
            self.generator.generate_password(exclude_chars=all_chars)
    
    def test_character_type_guarantee(self):
        """Test that generated passwords contain at least one character from each selected type."""
        password = self.generator.generate_password(
            length=20, use_lowercase=True, use_uppercase=True, 
            use_digits=True, use_symbols=True
        )
        
        self.assertTrue(any(c.islower() for c in password))
        self.assertTrue(any(c.isupper() for c in password))
        self.assertTrue(any(c.isdigit() for c in password))
        self.assertTrue(any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password))


class TestAdvancedPasswordGenerator(unittest.TestCase):
    """Test cases for the advanced GUI password generator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.generator = AdvancedPasswordGenerator()
    
    def test_secure_password_generation(self):
        """Test secure password generation with default settings."""
        password = self.generator.generate_secure_password()
        self.assertEqual(len(password), 16)  # Default length
        self.assertTrue(any(c.islower() for c in password))
        self.assertTrue(any(c.isupper() for c in password))
        self.assertTrue(any(c.isdigit() for c in password))
        self.assertTrue(any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password))
    
    def test_similar_character_exclusion(self):
        """Test exclusion of visually similar characters."""
        similar_chars = "il1Lo0O"
        password = self.generator.generate_secure_password(
            length=50, exclude_similar=True
        )
        for char in similar_chars:
            self.assertNotIn(char, password)
    
    def test_ambiguous_character_exclusion(self):
        """Test exclusion of ambiguous characters."""
        ambiguous_chars = "{}[]()/\\'\"`~,;.<>"
        password = self.generator.generate_secure_password(
            length=50, exclude_ambiguous=True
        )
        for char in ambiguous_chars:
            self.assertNotIn(char, password)
    
    def test_minimum_length_requirement(self):
        """Test minimum length requirement for security."""
        with self.assertRaises(ValueError):
            self.generator.generate_secure_password(length=3)
    
    def test_strength_enforcement(self):
        """Test that strength enforcement produces stronger passwords."""
        # Generate multiple passwords with strength enforcement
        strong_passwords = []
        for _ in range(10):
            password = self.generator.generate_secure_password(
                length=12, ensure_strength=True
            )
            strong_passwords.append(password)
        
        # Analyze strength of generated passwords
        analyzer = PasswordStrengthAnalyzer()
        for password in strong_passwords:
            score, strength, _ = analyzer.analyze_strength(password)
            # With strength enforcement, most passwords should be reasonably strong
            self.assertGreaterEqual(score, 40)  # At least medium strength


class TestPasswordStrengthAnalyzer(unittest.TestCase):
    """Test cases for the password strength analyzer."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.analyzer = PasswordStrengthAnalyzer()
    
    def test_length_scoring(self):
        """Test that longer passwords get higher scores."""
        short_password = "Aa1!"
        medium_password = "Aa1!Bb2@"
        long_password = "Aa1!Bb2@Cc3#Dd4$"
        
        short_score, _, _ = self.analyzer.analyze_strength(short_password)
        medium_score, _, _ = self.analyzer.analyze_strength(medium_password)
        long_score, _, _ = self.analyzer.analyze_strength(long_password)
        
        self.assertLess(short_score, medium_score)
        self.assertLess(medium_score, long_score)
    
    def test_character_variety_scoring(self):
        """Test that character variety affects scoring."""
        # Use longer passwords to ensure length doesn't dominate scoring
        only_lower = "abcdefghijklmnop"  # 15 chars, only lowercase
        mixed_case = "AbCdEfGhIjKlMnOp"  # 16 chars, mixed case
        with_digits = "AbCdEfGhIj123456"  # 16 chars, mixed + digits
        with_symbols = "AbCdEf123456!@#$"  # 16 chars, mixed + digits + symbols

        scores = []
        for password in [only_lower, mixed_case, with_digits, with_symbols]:
            score, _, _ = self.analyzer.analyze_strength(password)
            scores.append(score)

        # Each addition should generally improve the score
        self.assertLess(scores[0], scores[1])  # mixed case > only lower
        # Note: The next assertions might not always hold due to other factors
        # so we'll test that the final password (with all types) is stronger than the first
        self.assertLess(scores[0], scores[3])  # all types > only lowercase
    
    def test_pattern_detection(self):
        """Test detection of common patterns."""
        # Test repeated characters
        repeated_chars = "Aaaa1111!!!!"
        score, _, feedback = self.analyzer.analyze_strength(repeated_chars)
        self.assertTrue(any("repeat" in f.lower() for f in feedback))
        
        # Test sequential numbers
        sequential_nums = "Password123456"
        score, _, feedback = self.analyzer.analyze_strength(sequential_nums)
        self.assertTrue(any("sequential" in f.lower() for f in feedback))
        
        # Test sequential letters
        sequential_letters = "Passwordabc123"
        score, _, feedback = self.analyzer.analyze_strength(sequential_letters)
        self.assertTrue(any("sequential" in f.lower() for f in feedback))
    
    def test_common_word_detection(self):
        """Test detection of common words."""
        common_words = ["password", "admin", "user", "login", "welcome"]
        
        for word in common_words:
            test_password = f"{word}123!"
            score, _, feedback = self.analyzer.analyze_strength(test_password)
            self.assertTrue(any(word.lower() in f.lower() for f in feedback))
    
    def test_strength_levels(self):
        """Test strength level categorization."""
        # Very weak password
        very_weak = "123"
        score, strength, _ = self.analyzer.analyze_strength(very_weak)
        self.assertEqual(strength, "Very Weak")
        
        # Very strong password
        very_strong = "Tr0ub4dor&3XyZ!@#$%^&*"
        score, strength, _ = self.analyzer.analyze_strength(very_strong)
        self.assertIn(strength, ["Strong", "Very Strong"])
    
    def test_feedback_generation(self):
        """Test that appropriate feedback is generated."""
        weak_password = "pass"
        score, strength, feedback = self.analyzer.analyze_strength(weak_password)
        
        self.assertIsInstance(feedback, list)
        self.assertGreater(len(feedback), 0)  # Should have feedback for weak password
        
        # Check for common feedback types
        feedback_text = " ".join(feedback).lower()
        self.assertTrue(
            any(keyword in feedback_text for keyword in 
                ["short", "character", "mix", "uppercase", "lowercase", "digit", "symbol"])
        )


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system."""
    
    def test_cli_gui_consistency(self):
        """Test that CLI and GUI generators produce similar quality passwords."""
        cli_generator = PasswordGenerator()
        gui_generator = AdvancedPasswordGenerator()
        analyzer = PasswordStrengthAnalyzer()
        
        # Generate passwords with similar settings
        cli_passwords = []
        gui_passwords = []
        
        for _ in range(20):
            cli_password = cli_generator.generate_password(
                length=16, use_lowercase=True, use_uppercase=True,
                use_digits=True, use_symbols=True
            )
            gui_password = gui_generator.generate_secure_password(
                length=16, use_lowercase=True, use_uppercase=True,
                use_digits=True, use_symbols=True, ensure_strength=False
            )
            
            cli_passwords.append(cli_password)
            gui_passwords.append(gui_password)
        
        # Analyze average strength
        cli_scores = [analyzer.analyze_strength(p)[0] for p in cli_passwords]
        gui_scores = [analyzer.analyze_strength(p)[0] for p in gui_passwords]
        
        cli_avg = sum(cli_scores) / len(cli_scores)
        gui_avg = sum(gui_scores) / len(gui_scores)
        
        # Both should produce reasonably strong passwords
        self.assertGreater(cli_avg, 40)
        self.assertGreater(gui_avg, 40)
    
    def test_randomness_quality(self):
        """Test that generated passwords have good randomness properties."""
        generator = AdvancedPasswordGenerator()
        
        # Generate many passwords and check for patterns
        passwords = []
        for _ in range(100):
            password = generator.generate_secure_password(length=16)
            passwords.append(password)
        
        # Check that passwords are unique (very high probability)
        unique_passwords = set(passwords)
        self.assertEqual(len(unique_passwords), len(passwords))
        
        # Check character distribution
        all_chars = ''.join(passwords)
        char_counts = {}
        for char in all_chars:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        # No single character should dominate (basic randomness check)
        max_count = max(char_counts.values())
        total_chars = len(all_chars)
        max_frequency = max_count / total_chars
        
        # No character should appear more than 10% of the time (rough check)
        self.assertLess(max_frequency, 0.1)


def run_tests():
    """Run all tests and display results."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestPasswordGenerator,
        TestAdvancedPasswordGenerator,
        TestPasswordStrengthAnalyzer,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
