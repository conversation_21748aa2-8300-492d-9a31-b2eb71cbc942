#!/usr/bin/env python3
"""
Advanced GUI Password Generator
A sophisticated password generator with GUI interface, security rules, and clipboard integration.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import random
import string
import re
import pyperclip
from datetime import datetime


class PasswordStrengthAnalyzer:
    """Analyzes password strength and provides security feedback."""
    
    @staticmethod
    def analyze_strength(password):
        """
        Analyze password strength and return score and feedback.
        
        Args:
            password (str): Password to analyze
            
        Returns:
            tuple: (score, strength_level, feedback_list)
        """
        score = 0
        feedback = []
        
        # Length scoring
        length = len(password)
        if length >= 12:
            score += 25
        elif length >= 8:
            score += 15
            feedback.append("Consider using at least 12 characters for better security")
        else:
            score += 5
            feedback.append("Password is too short. Use at least 8 characters")
        
        # Character variety scoring
        has_lower = bool(re.search(r'[a-z]', password))
        has_upper = bool(re.search(r'[A-Z]', password))
        has_digit = bool(re.search(r'\d', password))
        has_symbol = bool(re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password))
        
        char_types = sum([has_lower, has_upper, has_digit, has_symbol])
        score += char_types * 15
        
        if char_types < 3:
            feedback.append("Use a mix of uppercase, lowercase, numbers, and symbols")
        
        # Pattern detection (reduce score for common patterns)
        if re.search(r'(.)\1{2,}', password):  # Repeated characters
            score -= 10
            feedback.append("Avoid repeating the same character multiple times")
        
        if re.search(r'(012|123|234|345|456|567|678|789|890)', password):
            score -= 15
            feedback.append("Avoid sequential numbers")
        
        if re.search(r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)', password.lower()):
            score -= 15
            feedback.append("Avoid sequential letters")
        
        # Common words check (basic)
        common_words = ['password', 'admin', 'user', 'login', 'welcome', '123456', 'qwerty']
        for word in common_words:
            if word.lower() in password.lower():
                score -= 20
                feedback.append(f"Avoid common words like '{word}'")
        
        # Determine strength level
        if score >= 80:
            strength = "Very Strong"
        elif score >= 60:
            strength = "Strong"
        elif score >= 40:
            strength = "Medium"
        elif score >= 20:
            strength = "Weak"
        else:
            strength = "Very Weak"
        
        return max(0, min(100, score)), strength, feedback


class AdvancedPasswordGenerator:
    """Advanced password generator with enhanced security features."""
    
    def __init__(self):
        self.lowercase = string.ascii_lowercase
        self.uppercase = string.ascii_uppercase
        self.digits = string.digits
        self.symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        self.similar_chars = "il1Lo0O"  # Characters that look similar
        self.ambiguous_chars = "{}[]()/\\'\"`~,;.<>"  # Potentially problematic chars
    
    def generate_secure_password(self, length=16, use_lowercase=True, use_uppercase=True,
                                use_digits=True, use_symbols=True, exclude_chars="",
                                exclude_similar=False, exclude_ambiguous=False,
                                ensure_strength=True):
        """
        Generate a secure password with advanced options.
        
        Args:
            length (int): Password length
            use_lowercase (bool): Include lowercase letters
            use_uppercase (bool): Include uppercase letters
            use_digits (bool): Include digits
            use_symbols (bool): Include symbols
            exclude_chars (str): Custom characters to exclude
            exclude_similar (bool): Exclude visually similar characters
            exclude_ambiguous (bool): Exclude ambiguous characters
            ensure_strength (bool): Ensure minimum strength requirements
            
        Returns:
            str: Generated secure password
        """
        if length < 4:
            raise ValueError("Password length must be at least 4 for security")
        
        # Build character sets
        char_sets = {}
        if use_lowercase:
            char_sets['lowercase'] = self.lowercase
        if use_uppercase:
            char_sets['uppercase'] = self.uppercase
        if use_digits:
            char_sets['digits'] = self.digits
        if use_symbols:
            char_sets['symbols'] = self.symbols
        
        if not char_sets:
            raise ValueError("At least one character type must be selected")
        
        # Apply exclusions
        exclusions = set(exclude_chars)
        if exclude_similar:
            exclusions.update(self.similar_chars)
        if exclude_ambiguous:
            exclusions.update(self.ambiguous_chars)
        
        # Filter character sets
        filtered_sets = {}
        for name, charset in char_sets.items():
            filtered = ''.join(c for c in charset if c not in exclusions)
            if filtered:
                filtered_sets[name] = filtered
        
        if not filtered_sets:
            raise ValueError("No valid characters remaining after exclusions")
        
        # Generate password with guaranteed character type representation
        password = []
        
        # Ensure at least one character from each selected type
        for charset in filtered_sets.values():
            password.append(random.choice(charset))
        
        # Fill remaining positions
        all_chars = ''.join(filtered_sets.values())
        remaining_length = length - len(password)
        
        for _ in range(remaining_length):
            password.append(random.choice(all_chars))
        
        # Shuffle to avoid predictable patterns
        random.shuffle(password)
        result = ''.join(password)
        
        # If strength checking is enabled, regenerate if password is too weak
        if ensure_strength:
            analyzer = PasswordStrengthAnalyzer()
            score, strength, _ = analyzer.analyze_strength(result)
            
            # Regenerate up to 10 times if password is weak
            attempts = 0
            while score < 60 and attempts < 10:
                password = []
                for charset in filtered_sets.values():
                    password.append(random.choice(charset))
                
                for _ in range(remaining_length):
                    password.append(random.choice(all_chars))
                
                random.shuffle(password)
                result = ''.join(password)
                score, strength, _ = analyzer.analyze_strength(result)
                attempts += 1
        
        return result


class PasswordGeneratorGUI:
    """Main GUI application for the advanced password generator."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Advanced Password Generator")
        self.root.geometry("600x700")
        self.root.resizable(True, True)
        
        # Initialize components
        self.generator = AdvancedPasswordGenerator()
        self.analyzer = PasswordStrengthAnalyzer()
        self.password_history = []
        
        # Create GUI
        self.create_widgets()
        
        # Generate initial password
        self.generate_password()
    
    def create_widgets(self):
        """Create and arrange GUI widgets."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Advanced Password Generator", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Password length
        ttk.Label(main_frame, text="Password Length:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.length_var = tk.IntVar(value=16)
        length_frame = ttk.Frame(main_frame)
        length_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        self.length_scale = ttk.Scale(length_frame, from_=4, to=64, 
                                     variable=self.length_var, orient=tk.HORIZONTAL)
        self.length_scale.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.length_label = ttk.Label(length_frame, text="16")
        self.length_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.length_scale.configure(command=self.update_length_label)
        
        # Character type options
        options_frame = ttk.LabelFrame(main_frame, text="Character Types", padding="10")
        options_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        options_frame.columnconfigure(1, weight=1)
        
        self.use_lowercase = tk.BooleanVar(value=True)
        self.use_uppercase = tk.BooleanVar(value=True)
        self.use_digits = tk.BooleanVar(value=True)
        self.use_symbols = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(options_frame, text="Lowercase (a-z)", 
                       variable=self.use_lowercase).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Uppercase (A-Z)", 
                       variable=self.use_uppercase).grid(row=0, column=1, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Digits (0-9)", 
                       variable=self.use_digits).grid(row=1, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Symbols (!@#$...)",
                       variable=self.use_symbols).grid(row=1, column=1, sticky=tk.W)

        # Advanced options
        advanced_frame = ttk.LabelFrame(main_frame, text="Advanced Options", padding="10")
        advanced_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        advanced_frame.columnconfigure(1, weight=1)

        self.exclude_similar = tk.BooleanVar(value=False)
        self.exclude_ambiguous = tk.BooleanVar(value=False)
        self.ensure_strength = tk.BooleanVar(value=True)

        ttk.Checkbutton(advanced_frame, text="Exclude similar chars (il1Lo0O)",
                       variable=self.exclude_similar).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(advanced_frame, text="Exclude ambiguous chars ({}[]()...)",
                       variable=self.exclude_ambiguous).grid(row=0, column=1, sticky=tk.W)
        ttk.Checkbutton(advanced_frame, text="Ensure strong password",
                       variable=self.ensure_strength).grid(row=1, column=0, sticky=tk.W)

        # Custom exclusions
        ttk.Label(advanced_frame, text="Exclude characters:").grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        self.exclude_chars_var = tk.StringVar()
        ttk.Entry(advanced_frame, textvariable=self.exclude_chars_var, width=30).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(10, 0))

        # Generate button
        generate_btn = ttk.Button(main_frame, text="Generate Password",
                                 command=self.generate_password, style='Accent.TButton')
        generate_btn.grid(row=4, column=0, columnspan=2, pady=20)

        # Password display
        password_frame = ttk.LabelFrame(main_frame, text="Generated Password", padding="10")
        password_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        password_frame.columnconfigure(0, weight=1)

        self.password_var = tk.StringVar()
        password_entry = ttk.Entry(password_frame, textvariable=self.password_var,
                                  font=('Courier', 12), state='readonly')
        password_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Password actions
        actions_frame = ttk.Frame(password_frame)
        actions_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

        ttk.Button(actions_frame, text="Copy to Clipboard",
                  command=self.copy_to_clipboard).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(actions_frame, text="Save to History",
                  command=self.save_to_history).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(actions_frame, text="Clear",
                  command=self.clear_password).pack(side=tk.LEFT)

        # Strength analysis
        strength_frame = ttk.LabelFrame(main_frame, text="Password Strength Analysis", padding="10")
        strength_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        strength_frame.columnconfigure(0, weight=1)

        # Strength meter
        meter_frame = ttk.Frame(strength_frame)
        meter_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        meter_frame.columnconfigure(1, weight=1)

        ttk.Label(meter_frame, text="Strength:").grid(row=0, column=0, sticky=tk.W)
        self.strength_var = tk.StringVar(value="Medium")
        self.strength_label = ttk.Label(meter_frame, textvariable=self.strength_var,
                                       font=('Arial', 10, 'bold'))
        self.strength_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        self.strength_progress = ttk.Progressbar(meter_frame, length=200, mode='determinate')
        self.strength_progress.grid(row=0, column=2, sticky=tk.E, padx=(10, 0))

        # Feedback
        self.feedback_text = scrolledtext.ScrolledText(strength_frame, height=4, width=50)
        self.feedback_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # History
        history_frame = ttk.LabelFrame(main_frame, text="Password History", padding="10")
        history_frame.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        history_frame.columnconfigure(0, weight=1)
        history_frame.rowconfigure(0, weight=1)

        # History listbox with scrollbar
        history_list_frame = ttk.Frame(history_frame)
        history_list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        history_list_frame.columnconfigure(0, weight=1)
        history_list_frame.rowconfigure(0, weight=1)

        self.history_listbox = tk.Listbox(history_list_frame, height=6)
        self.history_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        history_scrollbar = ttk.Scrollbar(history_list_frame, orient=tk.VERTICAL,
                                         command=self.history_listbox.yview)
        history_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.history_listbox.configure(yscrollcommand=history_scrollbar.set)

        # History actions
        history_actions = ttk.Frame(history_frame)
        history_actions.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Button(history_actions, text="Use Selected",
                  command=self.use_from_history).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(history_actions, text="Clear History",
                  command=self.clear_history).pack(side=tk.LEFT)

        # Configure main frame row weights for resizing
        main_frame.rowconfigure(7, weight=1)

    def update_length_label(self, value):
        """Update the length label when scale changes."""
        self.length_label.config(text=str(int(float(value))))

    def generate_password(self):
        """Generate a new password based on current settings."""
        try:
            password = self.generator.generate_secure_password(
                length=self.length_var.get(),
                use_lowercase=self.use_lowercase.get(),
                use_uppercase=self.use_uppercase.get(),
                use_digits=self.use_digits.get(),
                use_symbols=self.use_symbols.get(),
                exclude_chars=self.exclude_chars_var.get(),
                exclude_similar=self.exclude_similar.get(),
                exclude_ambiguous=self.exclude_ambiguous.get(),
                ensure_strength=self.ensure_strength.get()
            )

            self.password_var.set(password)
            self.analyze_password_strength(password)

        except ValueError as e:
            messagebox.showerror("Error", str(e))

    def analyze_password_strength(self, password):
        """Analyze and display password strength."""
        score, strength, feedback = self.analyzer.analyze_strength(password)

        # Update strength display
        self.strength_var.set(f"{strength} ({score}/100)")
        self.strength_progress['value'] = score

        # Color code the strength
        if score >= 80:
            self.strength_label.config(foreground='green')
        elif score >= 60:
            self.strength_label.config(foreground='orange')
        else:
            self.strength_label.config(foreground='red')

        # Update feedback
        self.feedback_text.delete(1.0, tk.END)
        if feedback:
            self.feedback_text.insert(tk.END, "Suggestions for improvement:\n")
            for i, suggestion in enumerate(feedback, 1):
                self.feedback_text.insert(tk.END, f"{i}. {suggestion}\n")
        else:
            self.feedback_text.insert(tk.END, "Excellent! This password meets all security criteria.")

    def copy_to_clipboard(self):
        """Copy the current password to clipboard."""
        password = self.password_var.get()
        if password:
            try:
                pyperclip.copy(password)
                messagebox.showinfo("Success", "Password copied to clipboard!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to copy to clipboard: {e}")
        else:
            messagebox.showwarning("Warning", "No password to copy!")

    def save_to_history(self):
        """Save the current password to history."""
        password = self.password_var.get()
        if password:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            entry = f"{timestamp} - {password}"
            self.password_history.append(entry)
            self.history_listbox.insert(tk.END, entry)
            messagebox.showinfo("Success", "Password saved to history!")
        else:
            messagebox.showwarning("Warning", "No password to save!")

    def use_from_history(self):
        """Use a password from history."""
        selection = self.history_listbox.curselection()
        if selection:
            entry = self.history_listbox.get(selection[0])
            # Extract password from the entry (after " - ")
            password = entry.split(" - ", 1)[1]
            self.password_var.set(password)
            self.analyze_password_strength(password)
        else:
            messagebox.showwarning("Warning", "Please select a password from history!")

    def clear_password(self):
        """Clear the current password."""
        self.password_var.set("")
        self.strength_var.set("No password")
        self.strength_progress['value'] = 0
        self.strength_label.config(foreground='black')
        self.feedback_text.delete(1.0, tk.END)

    def clear_history(self):
        """Clear the password history."""
        if messagebox.askyesno("Confirm", "Are you sure you want to clear the password history?"):
            self.password_history.clear()
            self.history_listbox.delete(0, tk.END)


def main():
    """Main function to run the GUI application."""
    try:
        root = tk.Tk()
        app = PasswordGeneratorGUI(root)
        root.mainloop()
    except ImportError as e:
        if "pyperclip" in str(e):
            print("Error: pyperclip module is required for clipboard functionality.")
            print("Install it using: pip install pyperclip")
        else:
            print(f"Import error: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()
