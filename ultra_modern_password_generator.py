#!/usr/bin/env python3
"""
Ultra Modern Password Generator with Stunning UI
Beautiful, animated interface with visual elements and smooth interactions.
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import random
import string
import pyperclip
from datetime import datetime
import threading
import time
import math
from PIL import Image, ImageTk, ImageDraw, ImageFilter
import io

from password_generator_gui import AdvancedPasswordGenerator, PasswordStrengthAnalyzer
from password_enhancer import PasswordEnhancer


class UltraModernTheme:
    """Ultra modern theme with gradients and animations."""
    
    COLORS = {
        # Gradient backgrounds
        'bg_gradient_start': '#0f0f23',
        'bg_gradient_end': '#1a1a2e',
        'card_gradient_start': '#16213e',
        'card_gradient_end': '#0f3460',
        
        # Accent colors
        'primary': '#00d4ff',      # Cyan blue
        'secondary': '#ff6b6b',    # Coral red
        'success': '#4ecdc4',      # Teal
        'warning': '#ffe66d',      # Yellow
        'danger': '#ff6b6b',       # Red
        
        # Text colors
        'text_primary': '#ffffff',
        'text_secondary': '#b8c5d6',
        'text_muted': '#6c7b8a',
        
        # Special effects
        'glow': '#00d4ff',
        'shadow': '#000000',
        'highlight': '#ffffff',
    }
    
    FONTS = {
        'title': ('Segoe UI', 24, 'bold'),
        'heading': ('Segoe UI', 18, 'bold'),
        'subheading': ('Segoe UI', 14, 'bold'),
        'body': ('Segoe UI', 11),
        'small': ('Segoe UI', 9),
        'mono': ('Consolas', 12),
        'mono_large': ('Consolas', 16, 'bold'),
    }


class AnimatedIcon:
    """Creates animated icons and visual elements."""
    
    @staticmethod
    def create_lock_icon(size=64, color='#00d4ff'):
        """Create an animated lock icon."""
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Lock body
        body_size = size * 0.6
        body_x = (size - body_size) // 2
        body_y = size * 0.4
        draw.rounded_rectangle(
            [body_x, body_y, body_x + body_size, body_y + body_size * 0.8],
            radius=size//8,
            fill=color
        )
        
        # Lock shackle
        shackle_size = size * 0.4
        shackle_x = (size - shackle_size) // 2
        shackle_y = size * 0.1
        draw.arc(
            [shackle_x, shackle_y, shackle_x + shackle_size, shackle_y + shackle_size],
            start=0, end=180,
            fill=color, width=size//12
        )
        
        return img
    
    @staticmethod
    def create_shield_icon(size=64, color='#4ecdc4'):
        """Create a shield icon for security."""
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Shield shape
        points = [
            (size//2, size*0.1),
            (size*0.9, size*0.3),
            (size*0.9, size*0.7),
            (size//2, size*0.95),
            (size*0.1, size*0.7),
            (size*0.1, size*0.3)
        ]
        draw.polygon(points, fill=color)
        
        # Checkmark
        check_points = [
            (size*0.3, size*0.5),
            (size*0.45, size*0.65),
            (size*0.7, size*0.35)
        ]
        draw.line(check_points, fill='white', width=size//12)
        
        return img
    
    @staticmethod
    def create_lightning_icon(size=64, color='#ffe66d'):
        """Create a lightning bolt icon for enhancement."""
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Lightning bolt shape
        points = [
            (size*0.6, size*0.1),
            (size*0.3, size*0.45),
            (size*0.5, size*0.45),
            (size*0.4, size*0.9),
            (size*0.7, size*0.55),
            (size*0.5, size*0.55)
        ]
        draw.polygon(points, fill=color)
        
        return img
    
    @staticmethod
    def create_gradient_background(width, height, start_color, end_color):
        """Create a gradient background."""
        img = Image.new('RGB', (width, height))
        draw = ImageDraw.Draw(img)
        
        # Parse colors
        start_rgb = tuple(int(start_color[i:i+2], 16) for i in (1, 3, 5))
        end_rgb = tuple(int(end_color[i:i+2], 16) for i in (1, 3, 5))
        
        for y in range(height):
            ratio = y / height
            r = int(start_rgb[0] * (1 - ratio) + end_rgb[0] * ratio)
            g = int(start_rgb[1] * (1 - ratio) + end_rgb[1] * ratio)
            b = int(start_rgb[2] * (1 - ratio) + end_rgb[2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        return img


class AnimatedProgressBar(tk.Canvas):
    """Custom animated progress bar with glow effects."""
    
    def __init__(self, parent, width=300, height=20, **kwargs):
        super().__init__(parent, width=width, height=height, 
                        highlightthickness=0, **kwargs)
        self.width = width
        self.height = height
        self.value = 0
        self.target_value = 0
        self.theme = UltraModernTheme()
        
        self.configure(bg=self.theme.COLORS['bg_gradient_start'])
        self.create_progress_bar()
        
    def create_progress_bar(self):
        """Create the progress bar elements."""
        # Background track
        self.create_rounded_rectangle(
            2, 2, self.width-2, self.height-2,
            radius=self.height//2,
            fill=self.theme.COLORS['bg_gradient_end'],
            outline=self.theme.COLORS['text_muted']
        )
        
        # Progress fill (initially empty)
        self.progress_rect = self.create_rounded_rectangle(
            2, 2, 2, self.height-2,
            radius=self.height//2,
            fill=self.theme.COLORS['primary'],
            outline=''
        )
    
    def create_rounded_rectangle(self, x1, y1, x2, y2, radius=10, **kwargs):
        """Create a rounded rectangle."""
        points = []
        for x, y in [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]:
            points.extend([x, y])
        return self.create_polygon(points, smooth=True, **kwargs)
    
    def animate_to(self, target_value):
        """Animate to target value with smooth transition."""
        self.target_value = max(0, min(100, target_value))
        self._animate_step()
    
    def _animate_step(self):
        """Single animation step."""
        if abs(self.value - self.target_value) < 0.5:
            self.value = self.target_value
            self._update_progress()
            return
        
        # Smooth easing
        diff = self.target_value - self.value
        self.value += diff * 0.1
        self._update_progress()
        
        self.after(16, self._animate_step)  # ~60 FPS
    
    def _update_progress(self):
        """Update the visual progress bar."""
        progress_width = (self.width - 4) * (self.value / 100)
        
        # Update progress rectangle
        self.coords(self.progress_rect, 2, 2, 2 + progress_width, self.height - 2)
        
        # Color based on value
        if self.value >= 80:
            color = self.theme.COLORS['success']
        elif self.value >= 60:
            color = self.theme.COLORS['primary']
        elif self.value >= 40:
            color = self.theme.COLORS['warning']
        else:
            color = self.theme.COLORS['danger']
        
        self.itemconfig(self.progress_rect, fill=color)


class GlowButton(tk.Canvas):
    """Custom button with glow effects and animations."""
    
    def __init__(self, parent, text="", command=None, style="primary", 
                 width=200, height=50, **kwargs):
        super().__init__(parent, width=width, height=height,
                        highlightthickness=0, **kwargs)
        
        self.text = text
        self.command = command
        self.style = style
        self.width = width
        self.height = height
        self.theme = UltraModernTheme()
        self.is_hovered = False
        self.is_pressed = False
        
        self.configure(bg=self.theme.COLORS['bg_gradient_start'])
        self.create_button()
        self.bind_events()
    
    def create_button(self):
        """Create the button elements."""
        # Button colors based on style
        if self.style == "primary":
            self.button_color = self.theme.COLORS['primary']
            self.text_color = self.theme.COLORS['bg_gradient_start']
        elif self.style == "success":
            self.button_color = self.theme.COLORS['success']
            self.text_color = self.theme.COLORS['bg_gradient_start']
        elif self.style == "danger":
            self.button_color = self.theme.COLORS['danger']
            self.text_color = self.theme.COLORS['text_primary']
        else:
            self.button_color = self.theme.COLORS['bg_gradient_end']
            self.text_color = self.theme.COLORS['text_primary']
        
        # Button background
        self.button_bg = self.create_rounded_rectangle(
            5, 5, self.width-5, self.height-5,
            radius=self.height//4,
            fill=self.button_color,
            outline=''
        )
        
        # Button text
        self.button_text = self.create_text(
            self.width//2, self.height//2,
            text=self.text,
            fill=self.text_color,
            font=self.theme.FONTS['body']
        )
    
    def create_rounded_rectangle(self, x1, y1, x2, y2, radius=10, **kwargs):
        """Create a rounded rectangle."""
        points = []
        steps = 20
        for i in range(steps):
            angle = math.pi * 2 * i / steps
            x = x1 + radius + (x2 - x1 - 2*radius) * (1 + math.cos(angle)) / 2
            y = y1 + radius + (y2 - y1 - 2*radius) * (1 + math.sin(angle)) / 2
            points.extend([x, y])
        return self.create_polygon(points, smooth=True, **kwargs)
    
    def bind_events(self):
        """Bind mouse events."""
        self.bind('<Enter>', self.on_enter)
        self.bind('<Leave>', self.on_leave)
        self.bind('<Button-1>', self.on_press)
        self.bind('<ButtonRelease-1>', self.on_release)
    
    def on_enter(self, event):
        """Handle mouse enter."""
        self.is_hovered = True
        self.animate_hover(True)
    
    def on_leave(self, event):
        """Handle mouse leave."""
        self.is_hovered = False
        self.animate_hover(False)
    
    def on_press(self, event):
        """Handle mouse press."""
        self.is_pressed = True
        self.animate_press(True)
    
    def on_release(self, event):
        """Handle mouse release."""
        self.is_pressed = False
        self.animate_press(False)
        if self.command and self.is_hovered:
            self.command()
    
    def animate_hover(self, hover):
        """Animate hover effect."""
        if hover:
            # Brighten color
            new_color = self._brighten_color(self.button_color, 1.2)
        else:
            new_color = self.button_color
        
        self.itemconfig(self.button_bg, fill=new_color)
    
    def animate_press(self, pressed):
        """Animate press effect."""
        if pressed:
            # Scale down slightly
            self.coords(self.button_bg, 7, 7, self.width-7, self.height-7)
        else:
            # Scale back up
            self.coords(self.button_bg, 5, 5, self.width-5, self.height-5)
    
    def _brighten_color(self, color, factor):
        """Brighten a hex color."""
        # Simple brightening - in a real app, you'd use proper color manipulation
        return color  # Simplified for now


class VisualSlider(tk.Canvas):
    """Beautiful visual slider for password length."""
    
    def __init__(self, parent, from_=8, to=64, value=16, command=None, **kwargs):
        super().__init__(parent, height=60, highlightthickness=0, **kwargs)
        
        self.from_ = from_
        self.to = to
        self.value = value
        self.command = command
        self.theme = UltraModernTheme()
        self.dragging = False
        
        self.configure(bg=self.theme.COLORS['bg_gradient_start'])
        self.bind('<Configure>', self.on_resize)
        self.bind('<Button-1>', self.on_click)
        self.bind('<B1-Motion>', self.on_drag)
        self.bind('<ButtonRelease-1>', self.on_release)
        
        self.after(100, self.create_slider)
    
    def on_resize(self, event):
        """Handle resize event."""
        self.width = event.width
        self.create_slider()
    
    def create_slider(self):
        """Create the slider elements."""
        if not hasattr(self, 'width'):
            self.width = self.winfo_width()
            if self.width <= 1:
                self.after(100, self.create_slider)
                return
        
        self.delete('all')
        
        # Slider track
        track_y = 30
        track_start = 40
        track_end = self.width - 40
        
        self.create_line(
            track_start, track_y, track_end, track_y,
            fill=self.theme.COLORS['text_muted'], width=4
        )
        
        # Value markers
        for i in range(5):
            x = track_start + (track_end - track_start) * i / 4
            marker_value = self.from_ + (self.to - self.from_) * i / 4
            
            self.create_line(x, track_y - 5, x, track_y + 5,
                           fill=self.theme.COLORS['text_secondary'], width=2)
            
            self.create_text(x, track_y + 15, text=str(int(marker_value)),
                           fill=self.theme.COLORS['text_secondary'],
                           font=self.theme.FONTS['small'])
        
        # Slider handle
        handle_x = self.value_to_x(self.value)
        self.handle = self.create_oval(
            handle_x - 12, track_y - 12, handle_x + 12, track_y + 12,
            fill=self.theme.COLORS['primary'],
            outline=self.theme.COLORS['text_primary'], width=2
        )
        
        # Value display
        self.value_text = self.create_text(
            handle_x, track_y - 25,
            text=str(self.value),
            fill=self.theme.COLORS['primary'],
            font=self.theme.FONTS['subheading']
        )
    
    def value_to_x(self, value):
        """Convert value to x coordinate."""
        if not hasattr(self, 'width'):
            return 40
        track_start = 40
        track_end = self.width - 40
        ratio = (value - self.from_) / (self.to - self.from_)
        return track_start + (track_end - track_start) * ratio
    
    def x_to_value(self, x):
        """Convert x coordinate to value."""
        track_start = 40
        track_end = self.width - 40
        ratio = (x - track_start) / (track_end - track_start)
        ratio = max(0, min(1, ratio))
        return int(self.from_ + (self.to - self.from_) * ratio)
    
    def on_click(self, event):
        """Handle click event."""
        self.dragging = True
        self.update_value(event.x)
    
    def on_drag(self, event):
        """Handle drag event."""
        if self.dragging:
            self.update_value(event.x)
    
    def on_release(self, event):
        """Handle release event."""
        self.dragging = False
    
    def update_value(self, x):
        """Update slider value."""
        new_value = self.x_to_value(x)
        if new_value != self.value:
            self.value = new_value
            self.create_slider()
            if self.command:
                self.command(self.value)
    
    def set_value(self, value):
        """Set slider value programmatically."""
        self.value = max(self.from_, min(self.to, value))
        self.create_slider()


class FloatingCard(tk.Frame):
    """Floating card with shadow effects."""

    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        self.theme = UltraModernTheme()
        self.title = title

        self.configure(
            bg=self.theme.COLORS['card_gradient_start'],
            relief='flat',
            bd=0
        )

        self.create_card()

    def create_card(self):
        """Create the card layout."""
        # Title bar
        if self.title:
            title_frame = tk.Frame(
                self,
                bg=self.theme.COLORS['primary'],
                height=40
            )
            title_frame.pack(fill='x')
            title_frame.pack_propagate(False)

            title_label = tk.Label(
                title_frame,
                text=self.title,
                font=self.theme.FONTS['subheading'],
                bg=self.theme.COLORS['primary'],
                fg=self.theme.COLORS['bg_gradient_start'],
                pady=10
            )
            title_label.pack()

        # Content area
        self.content_frame = tk.Frame(
            self,
            bg=self.theme.COLORS['card_gradient_start'],
            padx=20,
            pady=20
        )
        self.content_frame.pack(fill='both', expand=True)


class UltraModernPasswordApp:
    """Ultra modern password generator with stunning visuals."""

    def __init__(self, root):
        self.root = root
        self.theme = UltraModernTheme()

        # Initialize components
        self.generator = AdvancedPasswordGenerator()
        self.analyzer = PasswordStrengthAnalyzer()
        self.enhancer = PasswordEnhancer()

        # Application state
        self.password_history = []
        self.current_password = tk.StringVar()
        self.reference_password = tk.StringVar()

        # Animation state
        self.animations_running = []

        self.setup_window()
        self.create_main_interface()
        self.start_background_animations()

        # Generate initial password
        self.generate_new_password()

    def setup_window(self):
        """Configure the main window with stunning visuals."""
        self.root.title("🔐 Ultra Modern Password Generator")
        self.root.geometry("1200x900")
        self.root.minsize(1000, 800)

        # Create gradient background
        self.create_gradient_background()

        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.root.winfo_screenheight() // 2) - (900 // 2)
        self.root.geometry(f"1200x900+{x}+{y}")

        # Configure grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

    def create_gradient_background(self):
        """Create animated gradient background."""
        self.bg_canvas = tk.Canvas(
            self.root,
            highlightthickness=0
        )
        self.bg_canvas.place(x=0, y=0, relwidth=1, relheight=1)

        # Create gradient
        try:
            gradient_img = AnimatedIcon.create_gradient_background(
                1200, 900,
                self.theme.COLORS['bg_gradient_start'],
                self.theme.COLORS['bg_gradient_end']
            )
            self.bg_photo = ImageTk.PhotoImage(gradient_img)
            self.bg_canvas.create_image(0, 0, anchor='nw', image=self.bg_photo)
        except Exception:
            # Fallback to solid color
            self.bg_canvas.configure(bg=self.theme.COLORS['bg_gradient_start'])

        # Add floating particles
        self.create_floating_particles()

    def create_floating_particles(self):
        """Create floating particle effects."""
        self.particles = []
        for i in range(20):
            x = random.randint(0, 1200)
            y = random.randint(0, 900)
            size = random.randint(2, 6)

            particle = self.bg_canvas.create_oval(
                x, y, x + size, y + size,
                fill=self.theme.COLORS['primary'],
                outline='',
                stipple='gray25'
            )

            self.particles.append({
                'id': particle,
                'x': x,
                'y': y,
                'dx': random.uniform(-0.5, 0.5),
                'dy': random.uniform(-0.5, 0.5),
                'size': size
            })

    def animate_particles(self):
        """Animate floating particles."""
        for particle in self.particles:
            # Update position
            particle['x'] += particle['dx']
            particle['y'] += particle['dy']

            # Wrap around screen
            if particle['x'] < 0:
                particle['x'] = 1200
            elif particle['x'] > 1200:
                particle['x'] = 0

            if particle['y'] < 0:
                particle['y'] = 900
            elif particle['y'] > 900:
                particle['y'] = 0

            # Update canvas position
            size = particle['size']
            self.bg_canvas.coords(
                particle['id'],
                particle['x'], particle['y'],
                particle['x'] + size, particle['y'] + size
            )

        # Schedule next animation frame
        self.root.after(50, self.animate_particles)

    def create_main_interface(self):
        """Create the main interface with stunning visuals."""
        # Main container
        main_frame = tk.Frame(
            self.root,
            bg='',  # Transparent
            padx=30,
            pady=30
        )
        main_frame.place(relx=0, rely=0, relwidth=1, relheight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # Header with animated title
        self.create_animated_header(main_frame)

        # Main content area
        content_frame = tk.Frame(
            main_frame,
            bg=''  # Transparent
        )
        content_frame.grid(row=1, column=0, sticky='nsew', pady=(20, 0))
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)

        # Left panel - Generator
        self.create_generator_panel(content_frame)

        # Right panel - Results and Enhancement
        self.create_results_panel(content_frame)

    def create_animated_header(self, parent):
        """Create animated header with icons."""
        header_frame = tk.Frame(
            parent,
            bg='',
            height=100
        )
        header_frame.grid(row=0, column=0, sticky='ew')
        header_frame.pack_propagate(False)
        header_frame.columnconfigure(1, weight=1)

        # Animated lock icon
        try:
            lock_img = AnimatedIcon.create_lock_icon(64, self.theme.COLORS['primary'])
            self.lock_photo = ImageTk.PhotoImage(lock_img)

            icon_label = tk.Label(
                header_frame,
                image=self.lock_photo,
                bg=''
            )
            icon_label.grid(row=0, column=0, padx=(0, 20))
        except Exception:
            pass

        # Title with glow effect
        title_frame = tk.Frame(header_frame, bg='')
        title_frame.grid(row=0, column=1, sticky='w')

        title_label = tk.Label(
            title_frame,
            text="Ultra Modern Password Generator",
            font=self.theme.FONTS['title'],
            bg='',
            fg=self.theme.COLORS['text_primary']
        )
        title_label.pack(anchor='w')

        subtitle_label = tk.Label(
            title_frame,
            text="Generate • Enhance • Secure",
            font=self.theme.FONTS['body'],
            bg='',
            fg=self.theme.COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='w')

    def create_generator_panel(self, parent):
        """Create the generator panel with visual controls."""
        generator_card = FloatingCard(parent, title="🎲 Password Generator")
        generator_card.grid(row=0, column=0, sticky='nsew', padx=(0, 15))

        # Password length with visual slider
        length_section = tk.Frame(
            generator_card.content_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        length_section.pack(fill='x', pady=(0, 20))

        tk.Label(
            length_section,
            text="Password Length",
            font=self.theme.FONTS['subheading'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 10))

        self.length_slider = VisualSlider(
            length_section,
            from_=8, to=64, value=16,
            command=self.on_length_change,
            bg=self.theme.COLORS['card_gradient_start']
        )
        self.length_slider.pack(fill='x')

        # Character types with animated checkboxes
        self.create_character_options(generator_card.content_frame)

        # Generate button with glow effect
        self.generate_btn = GlowButton(
            generator_card.content_frame,
            text="🎲 Generate Secure Password",
            command=self.generate_new_password,
            style="primary",
            width=300, height=60,
            bg=self.theme.COLORS['card_gradient_start']
        )
        self.generate_btn.pack(pady=(30, 0))

    def create_character_options(self, parent):
        """Create animated character type options."""
        options_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['card_gradient_start']
        )
        options_frame.pack(fill='x', pady=(0, 20))

        tk.Label(
            options_frame,
            text="Character Types",
            font=self.theme.FONTS['subheading'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 15))

        # Character type options
        self.char_vars = {}
        options = [
            ('lowercase', '🔤 Lowercase (a-z)', True),
            ('uppercase', '🔠 Uppercase (A-Z)', True),
            ('digits', '🔢 Digits (0-9)', True),
            ('symbols', '🔣 Symbols (!@#$...)', True)
        ]

        for key, text, default in options:
            var = tk.BooleanVar(value=default)
            self.char_vars[key] = var

            option_frame = tk.Frame(
                options_frame,
                bg=self.theme.COLORS['card_gradient_start']
            )
            option_frame.pack(fill='x', pady=5)

            cb = tk.Checkbutton(
                option_frame,
                text=text,
                variable=var,
                font=self.theme.FONTS['body'],
                bg=self.theme.COLORS['card_gradient_start'],
                fg=self.theme.COLORS['text_primary'],
                selectcolor=self.theme.COLORS['primary'],
                activebackground=self.theme.COLORS['card_gradient_start'],
                activeforeground=self.theme.COLORS['text_primary'],
                relief='flat',
                command=self.on_settings_change
            )
            cb.pack(anchor='w')

    def create_results_panel(self, parent):
        """Create the results panel with visual feedback."""
        results_frame = tk.Frame(parent, bg='')
        results_frame.grid(row=0, column=1, sticky='nsew', padx=(15, 0))
        results_frame.rowconfigure(0, weight=1)
        results_frame.rowconfigure(1, weight=1)
        results_frame.columnconfigure(0, weight=1)

        # Password display card
        self.create_password_display(results_frame)

        # Enhancement card
        self.create_enhancement_panel(results_frame)

    def create_password_display(self, parent):
        """Create animated password display."""
        display_card = FloatingCard(parent, title="🔐 Generated Password")
        display_card.grid(row=0, column=0, sticky='nsew', pady=(0, 15))

        # Password display with glow effect
        password_frame = tk.Frame(
            display_card.content_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        password_frame.pack(fill='x', pady=(0, 20))

        self.password_display = tk.Entry(
            password_frame,
            textvariable=self.current_password,
            font=self.theme.FONTS['mono_large'],
            bg=self.theme.COLORS['bg_gradient_end'],
            fg=self.theme.COLORS['primary'],
            insertbackground=self.theme.COLORS['primary'],
            relief='flat',
            bd=10,
            state='readonly',
            readonlybackground=self.theme.COLORS['bg_gradient_end'],
            justify='center'
        )
        self.password_display.pack(fill='x', ipady=10)

        # Action buttons
        action_frame = tk.Frame(
            display_card.content_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        action_frame.pack(fill='x', pady=(0, 20))
        action_frame.columnconfigure(0, weight=1)
        action_frame.columnconfigure(1, weight=1)

        copy_btn = GlowButton(
            action_frame,
            text="📋 Copy",
            command=self.copy_password,
            style="success",
            width=140, height=45,
            bg=self.theme.COLORS['card_gradient_start']
        )
        copy_btn.grid(row=0, column=0, padx=(0, 10))

        save_btn = GlowButton(
            action_frame,
            text="💾 Save",
            command=self.save_to_history,
            style="secondary",
            width=140, height=45,
            bg=self.theme.COLORS['card_gradient_start']
        )
        save_btn.grid(row=0, column=1, padx=(10, 0))

        # Strength analysis with animated progress
        strength_frame = tk.Frame(
            display_card.content_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        strength_frame.pack(fill='x')

        tk.Label(
            strength_frame,
            text="Password Strength",
            font=self.theme.FONTS['subheading'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 10))

        # Strength meter
        meter_frame = tk.Frame(
            strength_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        meter_frame.pack(fill='x', pady=(0, 10))
        meter_frame.columnconfigure(0, weight=1)

        self.strength_progress = AnimatedProgressBar(
            meter_frame,
            width=300, height=25,
            bg=self.theme.COLORS['card_gradient_start']
        )
        self.strength_progress.grid(row=0, column=0, sticky='ew')

        self.strength_var = tk.StringVar(value="Medium")
        self.strength_label = tk.Label(
            meter_frame,
            textvariable=self.strength_var,
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['text_primary']
        )
        self.strength_label.grid(row=0, column=1, padx=(10, 0))

        # Feedback area
        self.feedback_text = tk.Text(
            strength_frame,
            height=6,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_gradient_end'],
            fg=self.theme.COLORS['text_secondary'],
            relief='flat',
            bd=8,
            wrap='word',
            state='disabled'
        )
        self.feedback_text.pack(fill='x')

    def create_enhancement_panel(self, parent):
        """Create password enhancement panel."""
        enhance_card = FloatingCard(parent, title="⚡ Password Enhancement")
        enhance_card.grid(row=1, column=0, sticky='nsew', pady=(15, 0))

        # Reference password input
        input_frame = tk.Frame(
            enhance_card.content_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        input_frame.pack(fill='x', pady=(0, 20))

        tk.Label(
            input_frame,
            text="Enter Your Current Password",
            font=self.theme.FONTS['subheading'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w', pady=(0, 10))

        self.ref_password_entry = tk.Entry(
            input_frame,
            textvariable=self.reference_password,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_gradient_end'],
            fg=self.theme.COLORS['text_primary'],
            insertbackground=self.theme.COLORS['primary'],
            relief='flat',
            bd=8,
            show='*'
        )
        self.ref_password_entry.pack(fill='x', ipady=8)

        # Show/hide toggle
        toggle_frame = tk.Frame(
            input_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        toggle_frame.pack(fill='x', pady=(10, 0))

        self.show_password_var = tk.BooleanVar()
        show_cb = tk.Checkbutton(
            toggle_frame,
            text="👁️ Show password",
            variable=self.show_password_var,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['text_secondary'],
            selectcolor=self.theme.COLORS['primary'],
            activebackground=self.theme.COLORS['card_gradient_start'],
            activeforeground=self.theme.COLORS['text_secondary'],
            relief='flat',
            command=self.toggle_password_visibility
        )
        show_cb.pack(anchor='w')

        # Enhancement options
        options_frame = tk.Frame(
            enhance_card.content_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        options_frame.pack(fill='x', pady=(0, 20))
        options_frame.columnconfigure(0, weight=1)
        options_frame.columnconfigure(1, weight=1)

        # Target length
        length_frame = tk.Frame(
            options_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        length_frame.grid(row=0, column=0, sticky='ew', padx=(0, 10))

        tk.Label(
            length_frame,
            text="Target Length",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w')

        self.target_length_slider = VisualSlider(
            length_frame,
            from_=8, to=32, value=16,
            bg=self.theme.COLORS['card_gradient_start']
        )
        self.target_length_slider.pack(fill='x', pady=(5, 0))

        # Readability option
        readability_frame = tk.Frame(
            options_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        readability_frame.grid(row=0, column=1, sticky='ew', padx=(10, 0))

        tk.Label(
            readability_frame,
            text="Options",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w')

        self.maintain_readability_var = tk.BooleanVar(value=True)
        readability_cb = tk.Checkbutton(
            readability_frame,
            text="🔤 Maintain readability",
            variable=self.maintain_readability_var,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['text_secondary'],
            selectcolor=self.theme.COLORS['primary'],
            activebackground=self.theme.COLORS['card_gradient_start'],
            activeforeground=self.theme.COLORS['text_secondary'],
            relief='flat'
        )
        readability_cb.pack(anchor='w', pady=(10, 0))

        # Enhance button
        enhance_btn = GlowButton(
            enhance_card.content_frame,
            text="⚡ Enhance Password",
            command=self.enhance_password,
            style="primary",
            width=300, height=50,
            bg=self.theme.COLORS['card_gradient_start']
        )
        enhance_btn.pack(pady=(0, 20))

        # Enhancement results
        self.results_frame = tk.Frame(
            enhance_card.content_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        self.results_frame.pack(fill='both', expand=True)

        # Before/After display (initially hidden)
        self.create_enhancement_results()

    def create_enhancement_results(self):
        """Create enhancement results display."""
        # Results will be shown after enhancement
        self.before_after_frame = tk.Frame(
            self.results_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )

        # Before/After labels
        comparison_frame = tk.Frame(
            self.before_after_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        comparison_frame.pack(fill='x', pady=(0, 10))
        comparison_frame.columnconfigure(0, weight=1)
        comparison_frame.columnconfigure(1, weight=1)

        # Before
        before_frame = tk.Frame(
            comparison_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        before_frame.grid(row=0, column=0, sticky='ew', padx=(0, 5))

        tk.Label(
            before_frame,
            text="Before:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['danger']
        ).pack(anchor='w')

        self.before_password_var = tk.StringVar()
        self.before_display = tk.Entry(
            before_frame,
            textvariable=self.before_password_var,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_gradient_end'],
            fg=self.theme.COLORS['danger'],
            state='readonly',
            relief='flat',
            bd=5,
            readonlybackground=self.theme.COLORS['bg_gradient_end']
        )
        self.before_display.pack(fill='x', pady=(5, 0))

        # After
        after_frame = tk.Frame(
            comparison_frame,
            bg=self.theme.COLORS['card_gradient_start']
        )
        after_frame.grid(row=0, column=1, sticky='ew', padx=(5, 0))

        tk.Label(
            after_frame,
            text="After:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['card_gradient_start'],
            fg=self.theme.COLORS['success']
        ).pack(anchor='w')

        self.after_password_var = tk.StringVar()
        self.after_display = tk.Entry(
            after_frame,
            textvariable=self.after_password_var,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_gradient_end'],
            fg=self.theme.COLORS['success'],
            state='readonly',
            relief='flat',
            bd=5,
            readonlybackground=self.theme.COLORS['bg_gradient_end']
        )
        self.after_display.pack(fill='x', pady=(5, 0))

        # Enhancement details
        self.enhancement_details = tk.Text(
            self.before_after_frame,
            height=4,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_gradient_end'],
            fg=self.theme.COLORS['text_secondary'],
            relief='flat',
            bd=5,
            wrap='word',
            state='disabled'
        )
        self.enhancement_details.pack(fill='x', pady=(10, 0))

    def start_background_animations(self):
        """Start background animations."""
        self.animate_particles()
        self.animate_title_glow()

    def animate_title_glow(self):
        """Animate title glow effect."""
        # This would create a pulsing glow effect on the title
        # Implementation would involve changing colors cyclically
        self.root.after(2000, self.animate_title_glow)

    # Core functionality methods
    def on_length_change(self, value):
        """Handle length slider change."""
        self.on_settings_change()

    def on_settings_change(self):
        """Handle settings change - auto-generate new password."""
        if hasattr(self, '_settings_timer'):
            self.root.after_cancel(self._settings_timer)
        self._settings_timer = self.root.after(300, self.generate_new_password)

    def generate_new_password(self):
        """Generate a new password with current settings."""
        try:
            password = self.generator.generate_secure_password(
                length=self.length_slider.value,
                use_lowercase=self.char_vars['lowercase'].get(),
                use_uppercase=self.char_vars['uppercase'].get(),
                use_digits=self.char_vars['digits'].get(),
                use_symbols=self.char_vars['symbols'].get(),
                exclude_chars="",
                exclude_similar=False,
                exclude_ambiguous=False,
                ensure_strength=True
            )

            self.current_password.set(password)
            self.analyze_current_password(password)
            self.animate_password_generation()

        except ValueError as e:
            self.show_notification(f"Generation Error: {e}", "error")

    def analyze_current_password(self, password):
        """Analyze and display password strength with animations."""
        score, strength, feedback = self.analyzer.analyze_strength(password)

        # Update strength display with animation
        self.strength_var.set(f"{strength} ({score}/100)")
        self.strength_progress.animate_to(score)

        # Update feedback
        self.feedback_text.config(state='normal')
        self.feedback_text.delete(1.0, tk.END)

        if feedback:
            self.feedback_text.insert(tk.END, "💡 Security Suggestions:\n\n")
            for i, suggestion in enumerate(feedback[:3], 1):
                self.feedback_text.insert(tk.END, f"{i}. {suggestion}\n")
        else:
            self.feedback_text.insert(tk.END, "✅ Excellent! This password is very secure.")

        self.feedback_text.config(state='disabled')

    def animate_password_generation(self):
        """Animate password generation effect."""
        # Flash effect on password display
        original_bg = self.password_display.cget('bg')
        self.password_display.config(bg=self.theme.COLORS['primary'])
        self.root.after(100, lambda: self.password_display.config(bg=original_bg))

    def copy_password(self):
        """Copy current password to clipboard with animation."""
        password = self.current_password.get()
        if password:
            try:
                pyperclip.copy(password)
                self.show_notification("Password copied to clipboard! 📋", "success")
                self.animate_copy_success()
            except Exception as e:
                self.show_notification(f"Failed to copy: {e}", "error")
        else:
            self.show_notification("No password to copy!", "warning")

    def animate_copy_success(self):
        """Animate successful copy operation."""
        # Brief glow effect on copy button
        pass  # Implementation would add visual feedback

    def save_to_history(self):
        """Save current password to history."""
        password = self.current_password.get()
        if password:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            score, strength, _ = self.analyzer.analyze_strength(password)

            entry = {
                'password': password,
                'timestamp': timestamp,
                'strength': strength,
                'score': score,
                'type': 'Generated'
            }

            self.password_history.append(entry)
            self.show_notification("Password saved to history! 💾", "success")
        else:
            self.show_notification("No password to save!", "warning")

    def toggle_password_visibility(self):
        """Toggle password visibility in enhancer."""
        if self.show_password_var.get():
            self.ref_password_entry.config(show='')
        else:
            self.ref_password_entry.config(show='*')

    def enhance_password(self):
        """Enhance the reference password with animations."""
        ref_password = self.reference_password.get().strip()
        if not ref_password:
            self.show_notification("Please enter a password to enhance! ⚡", "warning")
            return

        try:
            # Show loading animation
            self.show_enhancement_loading()

            # Perform enhancement
            result = self.enhancer.enhance_password(
                ref_password,
                target_length=self.target_length_slider.value,
                maintain_readability=self.maintain_readability_var.get()
            )

            # Show results with animation
            self.show_enhancement_results(result)

        except Exception as e:
            self.show_notification(f"Enhancement failed: {e}", "error")

    def show_enhancement_loading(self):
        """Show loading animation during enhancement."""
        # This would show a loading spinner or progress animation
        pass

    def show_enhancement_results(self, result):
        """Show enhancement results with animations."""
        # Update before/after display
        self.before_password_var.set(result['original'])
        self.after_password_var.set(result['enhanced'])

        # Show the results frame
        self.before_after_frame.pack(fill='x', pady=(10, 0))

        # Update enhancement details
        self.enhancement_details.config(state='normal')
        self.enhancement_details.delete(1.0, tk.END)

        orig_analysis = result['original_analysis']
        enh_analysis = result['enhanced_analysis']

        details = f"🚀 Enhancement Complete!\n\n"
        details += f"📈 Improvement: +{result['improvement']} points\n"
        details += f"🔧 Strategies: {', '.join(result['strategies_used'])}\n"
        details += f"💪 New Strength: {enh_analysis['strength']} ({enh_analysis['score']}/100)"

        self.enhancement_details.insert(tk.END, details)
        self.enhancement_details.config(state='disabled')

        self.show_notification("Password enhanced successfully! ⚡", "success")
        self.animate_enhancement_success()

    def animate_enhancement_success(self):
        """Animate successful enhancement."""
        # Flash effect on enhancement results
        pass

    def show_notification(self, message, type="info"):
        """Show animated notification."""
        # Create floating notification
        notification = tk.Toplevel(self.root)
        notification.wm_overrideredirect(True)
        notification.attributes('-topmost', True)

        # Position at top-right
        x = self.root.winfo_x() + self.root.winfo_width() - 350
        y = self.root.winfo_y() + 50
        notification.geometry(f"320x80+{x}+{y}")

        # Style based on type
        colors = {
            "success": self.theme.COLORS['success'],
            "error": self.theme.COLORS['danger'],
            "warning": self.theme.COLORS['warning'],
            "info": self.theme.COLORS['primary']
        }

        bg_color = colors.get(type, self.theme.COLORS['primary'])

        notification.configure(bg=bg_color)

        # Notification content
        content_frame = tk.Frame(notification, bg=bg_color, padx=20, pady=15)
        content_frame.pack(fill='both', expand=True)

        message_label = tk.Label(
            content_frame,
            text=message,
            font=self.theme.FONTS['body'],
            bg=bg_color,
            fg=self.theme.COLORS['bg_gradient_start'],
            wraplength=280
        )
        message_label.pack()

        # Auto-hide with fade animation
        self.animate_notification_fade(notification, 3000)

    def animate_notification_fade(self, notification, delay):
        """Animate notification fade out."""
        def fade_out():
            try:
                notification.destroy()
            except:
                pass

        notification.after(delay, fade_out)


def main():
    """Main function to run the ultra modern password generator."""
    try:
        root = tk.Tk()
        app = UltraModernPasswordApp(root)
        root.mainloop()
    except ImportError as e:
        if "PIL" in str(e) or "Pillow" in str(e):
            print("Error: Pillow (PIL) is required for image processing.")
            print("Install it using: pip install Pillow")
        elif "pyperclip" in str(e):
            print("Error: pyperclip module is required for clipboard functionality.")
            print("Install it using: pip install pyperclip")
        else:
            print(f"Import error: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()
