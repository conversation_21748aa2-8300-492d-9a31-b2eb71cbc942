from flask import Flask, render_template, request, jsonify
import random
import string
import secrets

app = Flask(__name__)

def make_password(length=12, use_lower=True, use_upper=True, use_nums=True, use_symbols=True, exclude=""):
    chars = ""
    if use_lower:
        chars += string.ascii_lowercase
    if use_upper:
        chars += string.ascii_uppercase
    if use_nums:
        chars += string.digits
    if use_symbols:
        chars += "!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    if exclude:
        chars = ''.join(c for c in chars if c not in exclude)
    
    if not chars:
        return "Error: No characters available"
    
    password = ''.join(secrets.choice(chars) for _ in range(length))
    return password

def check_strength(password):
    if not password:
        return 0, "No Password", ["Enter a password"]
    
    score = 0
    tips = []
    
    # Check length
    if len(password) >= 12:
        score += 30
    elif len(password) >= 8:
        score += 20
        tips.append("Try using at least 12 characters")
    else:
        score += 10
        tips.append("Password is too short")
    
    # Check character types
    has_lower = any(c.islower() for c in password)
    has_upper = any(c.isupper() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_symbol = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
    
    char_types = sum([has_lower, has_upper, has_digit, has_symbol])
    score += char_types * 15
    
    if not has_lower:
        tips.append("Add lowercase letters")
    if not has_upper:
        tips.append("Add uppercase letters")
    if not has_digit:
        tips.append("Add numbers")
    if not has_symbol:
        tips.append("Add symbols")
    
    # Determine strength level
    if score >= 80:
        level = "Very Strong"
    elif score >= 60:
        level = "Strong"
    elif score >= 40:
        level = "Medium"
    elif score >= 20:
        level = "Weak"
    else:
        level = "Very Weak"
    
    return score, level, tips

def improve_password(old_password, target_len=16):
    new_password = old_password
    
    # Simple character replacements
    replacements = {'a': '@', 'e': '3', 'i': '!', 'o': '0', 's': '$'}
    for old_char, new_char in replacements.items():
        if old_char in new_password.lower():
            new_password = new_password.replace(old_char, new_char, 1)
    
    # Add more characters if needed
    while len(new_password) < target_len:
        new_password += random.choice('!@#$%^&*123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ')
    
    return new_password

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate():
    try:
        data = request.get_json()
        
        length = int(data.get('length', 12))
        use_lower = bool(data.get('lowercase', True))
        use_upper = bool(data.get('uppercase', True))
        use_nums = bool(data.get('digits', True))
        use_symbols = bool(data.get('symbols', True))
        exclude = str(data.get('exclude', ''))
        
        if not any([use_lower, use_upper, use_nums, use_symbols]):
            return jsonify({'success': False, 'error': 'Select at least one character type'})
        
        password = make_password(length, use_lower, use_upper, use_nums, use_symbols, exclude)
        score, level, tips = check_strength(password)
        
        return jsonify({
            'success': True,
            'password': password,
            'strength': {
                'score': score,
                'level': level,
                'feedback': tips
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/enhance', methods=['POST'])
def enhance():
    try:
        data = request.get_json()
        old_pass = data.get('password', '')
        target_len = int(data.get('target_length', 16))
        
        if not old_pass:
            return jsonify({'success': False, 'error': 'No password provided'})
        
        new_pass = improve_password(old_pass, target_len)
        
        old_score, old_level, old_tips = check_strength(old_pass)
        new_score, new_level, new_tips = check_strength(new_pass)
        
        return jsonify({
            'success': True,
            'original': {
                'password': old_pass,
                'score': old_score,
                'level': old_level,
                'feedback': old_tips
            },
            'enhanced': {
                'password': new_pass,
                'score': new_score,
                'level': new_level,
                'feedback': new_tips
            },
            'improvement': new_score - old_score
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    app.run(debug=True, port=5000)
