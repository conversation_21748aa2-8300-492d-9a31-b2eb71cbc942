#!/usr/bin/env python3
"""
Demo script for the Password Generator project.
Demonstrates key features of both CLI and GUI versions.
"""

from password_generator_cli import PasswordGenerator
from password_generator_gui import AdvancedPasswordGenerator, PasswordStrengthAnalyzer


def demo_cli_generator():
    """Demonstrate CLI password generator features."""
    print("=" * 60)
    print("CLI PASSWORD GENERATOR DEMO")
    print("=" * 60)
    
    generator = PasswordGenerator()
    
    print("\n1. Default password generation:")
    password = generator.generate_password()
    print(f"   Generated: {password}")
    print(f"   Length: {len(password)}")
    
    print("\n2. Custom length passwords:")
    for length in [8, 16, 24]:
        password = generator.generate_password(length=length)
        print(f"   Length {length}: {password}")
    
    print("\n3. Character type variations:")
    
    # Only letters
    password = generator.generate_password(
        length=16, use_digits=False, use_symbols=False
    )
    print(f"   Letters only: {password}")
    
    # Only digits and symbols
    password = generator.generate_password(
        length=16, use_lowercase=False, use_uppercase=False
    )
    print(f"   Digits & symbols: {password}")
    
    # No symbols
    password = generator.generate_password(
        length=16, use_symbols=False
    )
    print(f"   No symbols: {password}")
    
    print("\n4. Character exclusion:")
    password = generator.generate_password(
        length=20, exclude_chars="aeiouAEIOU"
    )
    print(f"   No vowels: {password}")
    
    print("\n5. Multiple passwords:")
    print("   Batch generation:")
    for i in range(3):
        password = generator.generate_password(length=12)
        print(f"   Password {i+1}: {password}")


def demo_advanced_generator():
    """Demonstrate advanced GUI generator features."""
    print("\n" + "=" * 60)
    print("ADVANCED PASSWORD GENERATOR DEMO")
    print("=" * 60)
    
    generator = AdvancedPasswordGenerator()
    
    print("\n1. Secure password generation:")
    password = generator.generate_secure_password()
    print(f"   Generated: {password}")
    print(f"   Length: {len(password)}")
    
    print("\n2. Advanced exclusion options:")
    
    # Exclude similar characters
    password = generator.generate_secure_password(
        length=20, exclude_similar=True
    )
    print(f"   No similar chars: {password}")
    
    # Exclude ambiguous characters
    password = generator.generate_secure_password(
        length=20, exclude_ambiguous=True
    )
    print(f"   No ambiguous chars: {password}")
    
    # Both exclusions
    password = generator.generate_secure_password(
        length=20, exclude_similar=True, exclude_ambiguous=True
    )
    print(f"   Clean characters: {password}")
    
    print("\n3. Strength-enforced passwords:")
    for i in range(3):
        password = generator.generate_secure_password(
            length=14, ensure_strength=True
        )
        print(f"   Strong password {i+1}: {password}")
    
    print("\n4. Custom exclusions:")
    password = generator.generate_secure_password(
        length=18, exclude_chars="0O1lI"
    )
    print(f"   Custom exclusions: {password}")


def demo_strength_analyzer():
    """Demonstrate password strength analysis."""
    print("\n" + "=" * 60)
    print("PASSWORD STRENGTH ANALYZER DEMO")
    print("=" * 60)
    
    analyzer = PasswordStrengthAnalyzer()
    
    # Test passwords of varying strength
    test_passwords = [
        ("123", "Very weak password"),
        ("password", "Common word"),
        ("Password1", "Basic requirements"),
        ("P@ssw0rd123", "Mixed with symbols"),
        ("MyS3cur3P@ssw0rd!", "Strong password"),
        ("Tr0ub4dor&3XyZ!@#$%^", "Very strong password")
    ]
    
    print("\nPassword strength analysis:")
    print("-" * 50)
    
    for password, description in test_passwords:
        score, strength, feedback = analyzer.analyze_strength(password)
        
        print(f"\nPassword: {password}")
        print(f"Description: {description}")
        print(f"Strength: {strength} ({score}/100)")
        
        if feedback:
            print("Feedback:")
            for i, suggestion in enumerate(feedback[:3], 1):  # Show first 3 suggestions
                print(f"  {i}. {suggestion}")
        else:
            print("Feedback: Excellent password!")
        
        print("-" * 30)


def demo_pattern_detection():
    """Demonstrate pattern detection capabilities."""
    print("\n" + "=" * 60)
    print("PATTERN DETECTION DEMO")
    print("=" * 60)
    
    analyzer = PasswordStrengthAnalyzer()
    
    pattern_examples = [
        ("aaaa1111!!!!", "Repeated characters"),
        ("Password123456", "Sequential numbers"),
        ("Passwordabc123", "Sequential letters"),
        ("admin123!", "Common word 'admin'"),
        ("qwerty789", "Common word 'qwerty'"),
        ("MyP@ssw0rd2024", "Good pattern avoidance")
    ]
    
    print("\nPattern detection examples:")
    print("-" * 40)
    
    for password, pattern_type in pattern_examples:
        score, strength, feedback = analyzer.analyze_strength(password)
        
        print(f"\nPassword: {password}")
        print(f"Pattern type: {pattern_type}")
        print(f"Score: {score}/100")
        
        if feedback:
            relevant_feedback = [f for f in feedback if any(
                keyword in f.lower() for keyword in 
                ['repeat', 'sequential', 'common', 'word']
            )]
            if relevant_feedback:
                print(f"Detected: {relevant_feedback[0]}")
            else:
                print("No specific patterns detected")
        else:
            print("No patterns detected - good password!")


def demo_comparison():
    """Compare different password generation approaches."""
    print("\n" + "=" * 60)
    print("GENERATION APPROACH COMPARISON")
    print("=" * 60)
    
    cli_generator = PasswordGenerator()
    advanced_generator = AdvancedPasswordGenerator()
    analyzer = PasswordStrengthAnalyzer()
    
    print("\nComparing password quality:")
    print("-" * 40)
    
    approaches = [
        ("CLI Basic", lambda: cli_generator.generate_password(length=16)),
        ("GUI Standard", lambda: advanced_generator.generate_secure_password(
            length=16, ensure_strength=False)),
        ("GUI Strength-Enforced", lambda: advanced_generator.generate_secure_password(
            length=16, ensure_strength=True)),
        ("GUI Clean Chars", lambda: advanced_generator.generate_secure_password(
            length=16, exclude_similar=True, exclude_ambiguous=True))
    ]
    
    for approach_name, generator_func in approaches:
        print(f"\n{approach_name}:")
        
        # Generate 3 examples
        scores = []
        for i in range(3):
            password = generator_func()
            score, strength, _ = analyzer.analyze_strength(password)
            scores.append(score)
            print(f"  Example {i+1}: {password} ({strength}, {score}/100)")
        
        avg_score = sum(scores) / len(scores)
        print(f"  Average score: {avg_score:.1f}/100")


def main():
    """Run all demonstrations."""
    print("PASSWORD GENERATOR PROJECT DEMONSTRATION")
    print("This demo showcases the key features and capabilities")
    print("of both the CLI and GUI password generators.\n")
    
    try:
        # Run demonstrations
        demo_cli_generator()
        demo_advanced_generator()
        demo_strength_analyzer()
        demo_pattern_detection()
        demo_comparison()
        
        print("\n" + "=" * 60)
        print("DEMO COMPLETE")
        print("=" * 60)
        print("\nTo try the interactive versions:")
        print("- CLI version: python password_generator_cli.py")
        print("- GUI version: python password_generator_gui.py")
        print("- Run tests: python test_password_generator.py")
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure all required modules are available.")
    except Exception as e:
        print(f"Demo error: {e}")


if __name__ == "__main__":
    main()
