# Password Generator

A simple web-based password generator with a clean interface. Built with Flask for easy deployment and use.

## Features

### Web Interface (`main.py`)
- **Clean Design**: Simple and easy to use interface
- **Real-time Generation**: Passwords update as you change settings
- **Customizable Options**:
  - Password length from 4 to 64 characters
  - Character type selection
  - Character exclusion
- **Strength Analysis**: Visual strength meter with feedback
- **Copy to Clipboard**: One-click copy functionality
- **Password Enhancement**: Improve weak passwords
- **Mobile Friendly**: Works on all devices

### Command-Line Version (`password_generator_cli.py`)
- **Interactive Interface**: Simple command-line prompts
- **Customizable Length**: Password length from 1 to 128 characters
- **Character Selection**: Choose character types to include
- **Character Exclusion**: Exclude specific characters
- **Input Validation**: Proper validation for all inputs
- **Multiple Passwords**: Generate multiple passwords at once

## Installation

### Prerequisites
- Python 3.6 or higher
- pip (Python package installer)

### Setup
1. Clone or download this repository
2. Navigate to the project directory
3. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Dependencies
- **pyperclip**: For clipboard functionality in the GUI version
- **tkinter**: Built-in with Python (for GUI)

## Quick Start

### Web Interface
1. **Install Flask:**
   ```bash
   pip install Flask
   ```

2. **Run the application:**
   ```bash
   python main.py
   ```

3. **Open your browser:**
   ```
   http://localhost:5000
   ```

### Command-Line Version
```bash
python password_generator_cli.py
```

## File Structure

```
password_generator/
├── main.py                    # Flask web application
├── password_generator_cli.py  # Command-line version
├── requirements.txt           # Dependencies (Flask)
├── README.md                  # Documentation
├── templates/
│   └── index.html            # HTML template
└── static/
    ├── css/
    │   └── style.css         # Stylesheet
    └── js/
        └── script.js         # JavaScript functions
```

## How to Use

### Web Interface Features
- **Password Length**: Use the slider to set length (4-64 characters)
- **Character Types**: Check boxes for different character types
- **Exclude Characters**: Enter characters you don't want
- **Generate**: Click button to create password
- **Copy**: One-click copy to clipboard
- **Enhance**: Improve existing weak passwords

### Password Enhancement
1. Enter your current weak password
2. Set target length
3. Click "Enhance" to get a stronger version
4. See before/after comparison

## Security Features

### Password Strength Analysis
The GUI version includes a comprehensive strength analyzer that evaluates:
- **Length**: Longer passwords receive higher scores
- **Character Variety**: Mix of uppercase, lowercase, numbers, and symbols
- **Pattern Detection**: Identifies and penalizes common patterns
- **Common Words**: Detects and warns about dictionary words
- **Sequential Characters**: Flags sequential numbers or letters

### Strength Scoring
- **Very Strong (80-100)**: Excellent security
- **Strong (60-79)**: Good security with minor improvements possible
- **Medium (40-59)**: Adequate but could be stronger
- **Weak (20-39)**: Poor security, needs improvement
- **Very Weak (0-19)**: Unacceptable security level

### Security Best Practices Implemented
- Guaranteed character type representation
- Cryptographically secure randomization
- Pattern avoidance algorithms
- Configurable exclusion options
- Strength-based regeneration

## Code Structure

### Core Classes

#### `PasswordGenerator` (CLI)
- Basic password generation with user-defined criteria
- Input validation and error handling
- Character set management

#### `AdvancedPasswordGenerator` (GUI)
- Enhanced security features
- Advanced exclusion options
- Strength-based generation

#### `PasswordStrengthAnalyzer` (GUI)
- Comprehensive strength analysis
- Security feedback generation
- Pattern detection algorithms

#### `PasswordGeneratorGUI` (GUI)
- Complete Tkinter interface
- Event handling and user interactions
- History management and clipboard integration

## Key Programming Concepts Demonstrated

### 1. Randomization
- Secure random character selection
- Password shuffling to avoid patterns
- Cryptographically appropriate randomness

### 2. User Input Validation
- Type checking and range validation
- Error handling with user-friendly messages
- Default value management

### 3. Character Set Handling
- Dynamic character set construction
- Exclusion filtering
- Character type guarantees

### 4. GUI Design (Advanced Version)
- Responsive layout with proper widget arrangement
- Event-driven programming
- User experience optimization

### 5. Security Rules Implementation
- Password strength algorithms
- Pattern detection and avoidance
- Security best practices enforcement

### 6. Clipboard Integration
- Cross-platform clipboard access
- Error handling for clipboard operations
- User feedback for successful operations

## Customization Options

### CLI Version
- Password length (1-128 characters)
- Character type inclusion/exclusion
- Custom character exclusions
- Multiple generation sessions

### GUI Version
- All CLI features plus:
- Visual similar character exclusion
- Ambiguous character filtering
- Strength requirement enforcement
- History management
- Real-time strength feedback

## Error Handling

Both versions include comprehensive error handling for:
- Invalid input values
- Empty character sets
- Clipboard access failures
- System compatibility issues

## Future Enhancements

Potential improvements for advanced users:
- Password policy compliance checking
- Export/import functionality for password history
- Batch password generation
- Integration with password managers
- Custom character set definitions
- Pronunciation guides for generated passwords

## Contributing

This project is designed for educational purposes. Feel free to:
- Add new features
- Improve the user interface
- Enhance security algorithms
- Add unit tests
- Optimize performance

## License

This project is provided for educational purposes. Feel free to use, modify, and distribute as needed.

## Troubleshooting

### Common Issues

1. **"pyperclip module not found"**
   - Solution: Install with `pip install pyperclip`

2. **GUI doesn't start**
   - Ensure tkinter is available (usually built-in with Python)
   - Try: `python -m tkinter` to test tkinter installation

3. **Clipboard functionality not working**
   - Ensure pyperclip is installed and compatible with your system
   - Some Linux systems may require additional packages

4. **Password appears weak despite settings**
   - Try increasing length or enabling more character types
   - Disable "ensure strength" temporarily if needed

For additional help, check the error messages and ensure all dependencies are properly installed.
