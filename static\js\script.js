// Update length display when slider changes
document.getElementById('length').addEventListener('input', function() {
    document.getElementById('lengthDisplay').textContent = this.value;
});

// Auto-generate password when settings change
document.querySelectorAll('#length, #lowercase, #uppercase, #digits, #symbols, #exclude').forEach(element => {
    element.addEventListener('input', function() {
        setTimeout(generatePassword, 300);
    });
});

// Generate password function
async function generatePassword() {
    const data = {
        length: parseInt(document.getElementById('length').value),
        lowercase: document.getElementById('lowercase').checked,
        uppercase: document.getElementById('uppercase').checked,
        digits: document.getElementById('digits').checked,
        symbols: document.getElementById('symbols').checked,
        exclude: document.getElementById('exclude').value
    };

    try {
        const response = await fetch('/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            displayPassword(result.password, result.strength);
        } else {
            showNotification(result.error, 'error');
        }
    } catch (error) {
        showNotification('Failed to generate password', 'error');
    }
}

// Display generated password
function displayPassword(password, strength) {
    const display = document.getElementById('passwordDisplay');
    if (display) {
        display.textContent = password;
        display.className = 'password-display generated';
    }
    updateStrengthMeter(strength);
}

// Update strength meter
function updateStrengthMeter(strength) {
    const fill = document.getElementById('strengthFill');
    const text = document.getElementById('strengthText');
    const feedback = document.getElementById('feedback');
    
    if (fill) {
        fill.style.width = strength.score + '%';
        fill.className = 'strength-fill ' + strength.level.toLowerCase().replace(' ', '-');
    }
    
    if (text) {
        text.textContent = `Password Strength: ${strength.level} (${strength.score}/100)`;
    }
    
    if (feedback && strength.feedback) {
        if (strength.feedback.length > 0) {
            feedback.innerHTML = '<strong>Suggestions:</strong><ul>' + 
                strength.feedback.map(item => `<li>${item}</li>`).join('') + '</ul>';
        } else {
            feedback.innerHTML = '<strong>Excellent!</strong> This password is very secure.';
        }
    }
}

// Copy password to clipboard
async function copyPassword() {
    const passwordDisplay = document.getElementById('passwordDisplay');
    if (!passwordDisplay) return;
    
    const password = passwordDisplay.textContent;
    if (!password || password.includes('Click')) {
        showNotification('No password to copy', 'error');
        return;
    }
    
    try {
        await navigator.clipboard.writeText(password);
        showNotification('Password copied!', 'success');
    } catch (error) {
        showNotification('Failed to copy', 'error');
    }
}

// Enhance password function
async function enhancePassword() {
    const weakPassword = document.getElementById('weakPassword').value;
    const targetLength = parseInt(document.getElementById('targetLength').value);
    
    if (!weakPassword) {
        showNotification('Please enter a password to enhance', 'error');
        return;
    }

    try {
        const response = await fetch('/enhance', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                password: weakPassword,
                target_length: targetLength
            })
        });

        const result = await response.json();

        if (result.success) {
            showEnhancementResults(result);
        } else {
            showNotification(result.error, 'error');
        }
    } catch (error) {
        showNotification('Failed to enhance password', 'error');
    }
}

// Show enhancement results
function showEnhancementResults(result) {
    const elements = {
        beforePassword: document.getElementById('beforePassword'),
        afterPassword: document.getElementById('afterPassword'),
        beforeStrength: document.getElementById('beforeStrength'),
        afterStrength: document.getElementById('afterStrength'),
        improvementDetails: document.getElementById('improvementDetails'),
        enhancementResults: document.getElementById('enhancementResults')
    };
    
    if (elements.beforePassword) elements.beforePassword.textContent = result.original.password;
    if (elements.afterPassword) elements.afterPassword.textContent = result.enhanced.password;
    if (elements.beforeStrength) elements.beforeStrength.textContent = `${result.original.level} (${result.original.score}/100)`;
    if (elements.afterStrength) elements.afterStrength.textContent = `${result.enhanced.level} (${result.enhanced.score}/100)`;
    if (elements.improvementDetails) {
        elements.improvementDetails.innerHTML = `<strong>Improvement:</strong> +${result.improvement} points<br><strong>Your password is now much stronger!</strong>`;
    }
    if (elements.enhancementResults) {
        elements.enhancementResults.style.display = 'block';
    }
    
    showNotification('Password enhanced successfully!', 'success');
}

// Show notification
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => notification.classList.add('show'), 100);
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
}

// Generate initial password when page loads
generatePassword();
