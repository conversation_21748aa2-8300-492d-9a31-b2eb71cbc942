#!/usr/bin/env python3
"""
Modern Password Generator with Enhanced UI
A beautiful, user-friendly password generator with advanced features.
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import random
import string
import pyperclip
from datetime import datetime
import threading
import time

from password_generator_gui import AdvancedPasswordGenerator, PasswordStrengthAnalyzer
from password_enhancer import PasswordEnhancer


class ModernTheme:
    """Modern color theme and styling constants."""
    
    # Color palette - Modern dark theme with accent colors
    COLORS = {
        'bg_primary': '#1e1e2e',      # Dark background
        'bg_secondary': '#313244',     # Secondary background
        'bg_tertiary': '#45475a',      # Tertiary background
        'accent_primary': '#89b4fa',   # Blue accent
        'accent_secondary': '#a6e3a1', # Green accent
        'accent_danger': '#f38ba8',    # Red/pink accent
        'accent_warning': '#fab387',   # Orange accent
        'text_primary': '#cdd6f4',     # Primary text
        'text_secondary': '#bac2de',   # Secondary text
        'text_muted': '#6c7086',       # Muted text
        'border': '#585b70',           # Border color
        'success': '#a6e3a1',          # Success color
        'warning': '#f9e2af',          # Warning color
        'error': '#f38ba8',            # Error color
    }
    
    # Font configurations
    FONTS = {
        'heading': ('Segoe UI', 16, 'bold'),
        'subheading': ('Segoe UI', 12, 'bold'),
        'body': ('Segoe UI', 10),
        'small': ('Segoe UI', 9),
        'mono': ('Consolas', 11),
        'mono_large': ('Consolas', 14, 'bold'),
    }
    
    # Spacing and sizing
    SPACING = {
        'small': 5,
        'medium': 10,
        'large': 20,
        'xlarge': 30,
    }


class AnimatedProgressBar(ttk.Progressbar):
    """Custom animated progress bar for strength indication."""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.target_value = 0
        self.animation_speed = 2
        
    def animate_to(self, target_value):
        """Animate progress bar to target value."""
        self.target_value = target_value
        self._animate_step()
    
    def _animate_step(self):
        """Single animation step."""
        current = self['value']
        if abs(current - self.target_value) < 1:
            self['value'] = self.target_value
            return
        
        step = (self.target_value - current) / 10
        self['value'] = current + step
        self.after(50, self._animate_step)


class ModernButton(tk.Button):
    """Custom modern button with hover effects."""
    
    def __init__(self, parent, text="", command=None, style="primary", **kwargs):
        self.style = style
        self.theme = ModernTheme()
        
        # Set button colors based on style
        if style == "primary":
            bg_color = self.theme.COLORS['accent_primary']
            fg_color = self.theme.COLORS['bg_primary']
            hover_color = '#74c0fc'
        elif style == "secondary":
            bg_color = self.theme.COLORS['bg_secondary']
            fg_color = self.theme.COLORS['text_primary']
            hover_color = self.theme.COLORS['bg_tertiary']
        elif style == "success":
            bg_color = self.theme.COLORS['accent_secondary']
            fg_color = self.theme.COLORS['bg_primary']
            hover_color = '#94e2d5'
        elif style == "danger":
            bg_color = self.theme.COLORS['accent_danger']
            fg_color = self.theme.COLORS['bg_primary']
            hover_color = '#eba0ac'
        else:
            bg_color = self.theme.COLORS['bg_tertiary']
            fg_color = self.theme.COLORS['text_primary']
            hover_color = '#585b70'
        
        super().__init__(
            parent,
            text=text,
            command=command,
            bg=bg_color,
            fg=fg_color,
            font=self.theme.FONTS['body'],
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2',
            **kwargs
        )
        
        self.default_bg = bg_color
        self.hover_bg = hover_color
        
        # Bind hover events
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)
    
    def _on_enter(self, event):
        """Handle mouse enter event."""
        self.config(bg=self.hover_bg)
    
    def _on_leave(self, event):
        """Handle mouse leave event."""
        self.config(bg=self.default_bg)


class ModernFrame(tk.Frame):
    """Custom frame with modern styling."""
    
    def __init__(self, parent, **kwargs):
        theme = ModernTheme()
        super().__init__(
            parent,
            bg=theme.COLORS['bg_secondary'],
            relief='flat',
            bd=1,
            highlightbackground=theme.COLORS['border'],
            highlightthickness=1,
            **kwargs
        )


class PasswordCard(ModernFrame):
    """Card component for displaying password information."""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        self.theme = ModernTheme()
        
        # Title
        title_label = tk.Label(
            self,
            text=title,
            font=self.theme.FONTS['subheading'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        )
        title_label.pack(anchor='w', padx=15, pady=(15, 5))
        
        # Content frame
        self.content_frame = tk.Frame(
            self,
            bg=self.theme.COLORS['bg_secondary']
        )
        self.content_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))


class TooltipManager:
    """Manages tooltips for UI elements."""
    
    def __init__(self):
        self.tooltip = None
    
    def create_tooltip(self, widget, text):
        """Create a tooltip for a widget."""
        def on_enter(event):
            self.show_tooltip(event, text)
        
        def on_leave(event):
            self.hide_tooltip()
        
        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)
    
    def show_tooltip(self, event, text):
        """Show tooltip at cursor position."""
        if self.tooltip:
            self.hide_tooltip()
        
        self.tooltip = tk.Toplevel()
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_geometry(f"+{event.x_root + 10}+{event.y_root + 10}")
        
        theme = ModernTheme()
        label = tk.Label(
            self.tooltip,
            text=text,
            background=theme.COLORS['bg_tertiary'],
            foreground=theme.COLORS['text_primary'],
            font=theme.FONTS['small'],
            relief='solid',
            borderwidth=1,
            padx=8,
            pady=4
        )
        label.pack()
    
    def hide_tooltip(self):
        """Hide the current tooltip."""
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None


class ModernPasswordGeneratorApp:
    """Main application class with modern UI."""
    
    def __init__(self, root):
        self.root = root
        self.theme = ModernTheme()
        self.tooltip_manager = TooltipManager()
        
        # Initialize components
        self.generator = AdvancedPasswordGenerator()
        self.analyzer = PasswordStrengthAnalyzer()
        self.enhancer = PasswordEnhancer()
        
        # Application state
        self.password_history = []
        self.current_password = tk.StringVar()
        self.reference_password = tk.StringVar()
        
        # Setup the application
        self.setup_window()
        self.create_styles()
        self.create_main_interface()
        
        # Generate initial password
        self.generate_new_password()
    
    def setup_window(self):
        """Configure the main window."""
        self.root.title("🔐 Modern Password Generator")
        self.root.geometry("900x800")
        self.root.minsize(800, 700)
        self.root.configure(bg=self.theme.COLORS['bg_primary'])
        
        # Center window on screen
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (900 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"900x800+{x}+{y}")
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def create_styles(self):
        """Create custom ttk styles."""
        style = ttk.Style()
        
        # Configure notebook (tabs) style
        style.configure(
            'Modern.TNotebook',
            background=self.theme.COLORS['bg_primary'],
            borderwidth=0
        )
        
        style.configure(
            'Modern.TNotebook.Tab',
            background=self.theme.COLORS['bg_secondary'],
            foreground=self.theme.COLORS['text_primary'],
            padding=[20, 10],
            font=self.theme.FONTS['body']
        )
        
        style.map(
            'Modern.TNotebook.Tab',
            background=[('selected', self.theme.COLORS['accent_primary'])],
            foreground=[('selected', self.theme.COLORS['bg_primary'])]
        )
        
        # Configure scale style
        style.configure(
            'Modern.Horizontal.TScale',
            background=self.theme.COLORS['bg_secondary'],
            troughcolor=self.theme.COLORS['bg_tertiary'],
            borderwidth=0,
            lightcolor=self.theme.COLORS['accent_primary'],
            darkcolor=self.theme.COLORS['accent_primary']
        )
    
    def create_main_interface(self):
        """Create the main user interface."""
        # Main container
        main_container = tk.Frame(
            self.root,
            bg=self.theme.COLORS['bg_primary'],
            padx=20,
            pady=20
        )
        main_container.grid(row=0, column=0, sticky='nsew')
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(1, weight=1)
        
        # Header
        self.create_header(main_container)
        
        # Notebook for tabs
        self.notebook = ttk.Notebook(main_container, style='Modern.TNotebook')
        self.notebook.grid(row=1, column=0, sticky='nsew', pady=(20, 0))
        
        # Create tabs
        self.create_generator_tab()
        self.create_enhancer_tab()
        self.create_history_tab()
    
    def create_header(self, parent):
        """Create the application header."""
        header_frame = tk.Frame(
            parent,
            bg=self.theme.COLORS['bg_primary']
        )
        header_frame.grid(row=0, column=0, sticky='ew')
        header_frame.columnconfigure(1, weight=1)
        
        # App icon and title
        title_label = tk.Label(
            header_frame,
            text="🔐 Password Generator Pro",
            font=self.theme.FONTS['heading'],
            bg=self.theme.COLORS['bg_primary'],
            fg=self.theme.COLORS['text_primary']
        )
        title_label.grid(row=0, column=0, sticky='w')
        
        # Subtitle
        subtitle_label = tk.Label(
            header_frame,
            text="Generate secure passwords with modern design",
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_primary'],
            fg=self.theme.COLORS['text_muted']
        )
        subtitle_label.grid(row=1, column=0, sticky='w', pady=(0, 10))
    
    def create_generator_tab(self):
        """Create the password generator tab."""
        # Create tab frame
        gen_frame = tk.Frame(self.notebook, bg=self.theme.COLORS['bg_primary'])
        self.notebook.add(gen_frame, text="🎲 Generate")
        
        # Configure grid
        gen_frame.columnconfigure(0, weight=1)
        gen_frame.columnconfigure(1, weight=1)
        
        # Left column - Settings
        self.create_generator_settings(gen_frame)
        
        # Right column - Results
        self.create_generator_results(gen_frame)
    
    def create_enhancer_tab(self):
        """Create the password enhancer tab."""
        # Create tab frame
        enh_frame = tk.Frame(self.notebook, bg=self.theme.COLORS['bg_primary'])
        self.notebook.add(enh_frame, text="⚡ Enhance")
        
        # Configure grid
        enh_frame.columnconfigure(0, weight=1)
        
        # Create enhancer interface
        self.create_enhancer_interface(enh_frame)
    
    def create_history_tab(self):
        """Create the password history tab."""
        # Create tab frame
        hist_frame = tk.Frame(self.notebook, bg=self.theme.COLORS['bg_primary'])
        self.notebook.add(hist_frame, text="📚 History")
        
        # Configure grid
        hist_frame.columnconfigure(0, weight=1)
        hist_frame.rowconfigure(1, weight=1)
        
        # Create history interface
        self.create_history_interface(hist_frame)

    def create_generator_settings(self, parent):
        """Create the generator settings panel."""
        settings_card = PasswordCard(parent, title="⚙️ Generation Settings")
        settings_card.grid(row=0, column=0, sticky='nsew', padx=(0, 10), pady=10)

        # Password length
        length_frame = tk.Frame(settings_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        length_frame.pack(fill='x', pady=(0, 15))

        length_label = tk.Label(
            length_frame,
            text="Password Length:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        )
        length_label.pack(anchor='w')

        # Length control frame
        length_control = tk.Frame(length_frame, bg=self.theme.COLORS['bg_secondary'])
        length_control.pack(fill='x', pady=(5, 0))
        length_control.columnconfigure(0, weight=1)

        self.length_var = tk.IntVar(value=16)
        self.length_scale = ttk.Scale(
            length_control,
            from_=8,
            to=64,
            variable=self.length_var,
            orient='horizontal',
            style='Modern.Horizontal.TScale',
            command=self.on_length_change
        )
        self.length_scale.grid(row=0, column=0, sticky='ew', padx=(0, 10))

        self.length_display = tk.Label(
            length_control,
            text="16",
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['accent_primary'],
            padx=10,
            pady=5,
            relief='flat'
        )
        self.length_display.grid(row=0, column=1)

        # Character types
        char_types_frame = tk.Frame(settings_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        char_types_frame.pack(fill='x', pady=(0, 15))

        char_label = tk.Label(
            char_types_frame,
            text="Character Types:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        )
        char_label.pack(anchor='w', pady=(0, 10))

        # Character type checkboxes
        self.char_vars = {}
        char_options = [
            ('lowercase', 'Lowercase (a-z)', True),
            ('uppercase', 'Uppercase (A-Z)', True),
            ('digits', 'Digits (0-9)', True),
            ('symbols', 'Symbols (!@#$...)', True)
        ]

        for key, text, default in char_options:
            var = tk.BooleanVar(value=default)
            self.char_vars[key] = var

            cb = tk.Checkbutton(
                char_types_frame,
                text=text,
                variable=var,
                font=self.theme.FONTS['body'],
                bg=self.theme.COLORS['bg_secondary'],
                fg=self.theme.COLORS['text_primary'],
                selectcolor=self.theme.COLORS['bg_tertiary'],
                activebackground=self.theme.COLORS['bg_secondary'],
                activeforeground=self.theme.COLORS['text_primary'],
                relief='flat',
                command=self.on_settings_change
            )
            cb.pack(anchor='w', pady=2)

        # Advanced options
        advanced_frame = tk.Frame(settings_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        advanced_frame.pack(fill='x', pady=(0, 15))

        advanced_label = tk.Label(
            advanced_frame,
            text="Advanced Options:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        )
        advanced_label.pack(anchor='w', pady=(0, 10))

        self.advanced_vars = {}
        advanced_options = [
            ('exclude_similar', 'Exclude similar chars (il1Lo0O)', False),
            ('exclude_ambiguous', 'Exclude ambiguous chars ({}[]()...)', False),
            ('ensure_strength', 'Ensure strong password', True)
        ]

        for key, text, default in advanced_options:
            var = tk.BooleanVar(value=default)
            self.advanced_vars[key] = var

            cb = tk.Checkbutton(
                advanced_frame,
                text=text,
                variable=var,
                font=self.theme.FONTS['small'],
                bg=self.theme.COLORS['bg_secondary'],
                fg=self.theme.COLORS['text_secondary'],
                selectcolor=self.theme.COLORS['bg_tertiary'],
                activebackground=self.theme.COLORS['bg_secondary'],
                activeforeground=self.theme.COLORS['text_secondary'],
                relief='flat',
                command=self.on_settings_change
            )
            cb.pack(anchor='w', pady=1)

        # Custom exclusions
        exclusion_frame = tk.Frame(settings_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        exclusion_frame.pack(fill='x', pady=(0, 15))

        exclusion_label = tk.Label(
            exclusion_frame,
            text="Exclude Characters:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        )
        exclusion_label.pack(anchor='w', pady=(0, 5))

        self.exclude_chars_var = tk.StringVar()
        exclude_entry = tk.Entry(
            exclusion_frame,
            textvariable=self.exclude_chars_var,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_primary'],
            insertbackground=self.theme.COLORS['accent_primary'],
            relief='flat',
            bd=5
        )
        exclude_entry.pack(fill='x')
        exclude_entry.bind('<KeyRelease>', lambda e: self.on_settings_change())

        # Generate button
        generate_btn = ModernButton(
            settings_card.content_frame,
            text="🎲 Generate New Password",
            command=self.generate_new_password,
            style="primary"
        )
        generate_btn.pack(fill='x', pady=(20, 0))

    def create_generator_results(self, parent):
        """Create the generator results panel."""
        results_card = PasswordCard(parent, title="🔐 Generated Password")
        results_card.grid(row=0, column=1, sticky='nsew', padx=(10, 0), pady=10)

        # Password display
        password_frame = tk.Frame(results_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        password_frame.pack(fill='x', pady=(0, 15))

        self.password_display = tk.Entry(
            password_frame,
            textvariable=self.current_password,
            font=self.theme.FONTS['mono_large'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['accent_primary'],
            insertbackground=self.theme.COLORS['accent_primary'],
            relief='flat',
            bd=10,
            state='readonly',
            readonlybackground=self.theme.COLORS['bg_tertiary']
        )
        self.password_display.pack(fill='x')

        # Action buttons
        action_frame = tk.Frame(results_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        action_frame.pack(fill='x', pady=(0, 15))
        action_frame.columnconfigure(0, weight=1)
        action_frame.columnconfigure(1, weight=1)

        copy_btn = ModernButton(
            action_frame,
            text="📋 Copy",
            command=self.copy_password,
            style="success"
        )
        copy_btn.grid(row=0, column=0, sticky='ew', padx=(0, 5))

        save_btn = ModernButton(
            action_frame,
            text="💾 Save",
            command=self.save_to_history,
            style="secondary"
        )
        save_btn.grid(row=0, column=1, sticky='ew', padx=(5, 0))

        # Strength analysis
        strength_card = PasswordCard(results_card.content_frame, title="📊 Strength Analysis")
        strength_card.pack(fill='both', expand=True, pady=(20, 0))

        # Strength meter
        meter_frame = tk.Frame(strength_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        meter_frame.pack(fill='x', pady=(0, 10))
        meter_frame.columnconfigure(1, weight=1)

        strength_label = tk.Label(
            meter_frame,
            text="Strength:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        )
        strength_label.grid(row=0, column=0, sticky='w')

        self.strength_var = tk.StringVar(value="Medium")
        self.strength_display = tk.Label(
            meter_frame,
            textvariable=self.strength_var,
            font=self.theme.FONTS['subheading'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['accent_secondary']
        )
        self.strength_display.grid(row=0, column=1, sticky='w', padx=(10, 0))

        self.strength_progress = AnimatedProgressBar(
            meter_frame,
            length=200,
            mode='determinate',
            style='TProgressbar'
        )
        self.strength_progress.grid(row=0, column=2, sticky='e')

        # Feedback area
        feedback_frame = tk.Frame(strength_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        feedback_frame.pack(fill='both', expand=True)

        self.feedback_text = tk.Text(
            feedback_frame,
            height=6,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_secondary'],
            relief='flat',
            bd=5,
            wrap='word',
            state='disabled'
        )
        self.feedback_text.pack(fill='both', expand=True)

        # Scrollbar for feedback
        feedback_scroll = ttk.Scrollbar(feedback_frame, orient='vertical', command=self.feedback_text.yview)
        self.feedback_text.configure(yscrollcommand=feedback_scroll.set)

    def create_enhancer_interface(self, parent):
        """Create the password enhancer interface."""
        # Input section
        input_card = PasswordCard(parent, title="📝 Enter Your Current Password")
        input_card.pack(fill='x', padx=20, pady=(20, 10))

        # Reference password input
        ref_frame = tk.Frame(input_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        ref_frame.pack(fill='x', pady=(0, 15))

        ref_label = tk.Label(
            ref_frame,
            text="Current Password:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        )
        ref_label.pack(anchor='w', pady=(0, 5))

        self.ref_password_entry = tk.Entry(
            ref_frame,
            textvariable=self.reference_password,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_primary'],
            insertbackground=self.theme.COLORS['accent_primary'],
            relief='flat',
            bd=8,
            show='*'
        )
        self.ref_password_entry.pack(fill='x')

        # Show/hide password toggle
        toggle_frame = tk.Frame(ref_frame, bg=self.theme.COLORS['bg_secondary'])
        toggle_frame.pack(fill='x', pady=(5, 0))

        self.show_password_var = tk.BooleanVar()
        show_cb = tk.Checkbutton(
            toggle_frame,
            text="Show password",
            variable=self.show_password_var,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_muted'],
            selectcolor=self.theme.COLORS['bg_tertiary'],
            activebackground=self.theme.COLORS['bg_secondary'],
            activeforeground=self.theme.COLORS['text_muted'],
            relief='flat',
            command=self.toggle_password_visibility
        )
        show_cb.pack(anchor='w')

        # Enhancement options
        options_frame = tk.Frame(input_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        options_frame.pack(fill='x', pady=(0, 15))
        options_frame.columnconfigure(0, weight=1)
        options_frame.columnconfigure(1, weight=1)

        # Target length
        length_frame = tk.Frame(options_frame, bg=self.theme.COLORS['bg_secondary'])
        length_frame.grid(row=0, column=0, sticky='ew', padx=(0, 10))

        tk.Label(
            length_frame,
            text="Target Length:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w')

        self.target_length_var = tk.IntVar(value=16)
        target_scale = ttk.Scale(
            length_frame,
            from_=8,
            to=32,
            variable=self.target_length_var,
            orient='horizontal',
            style='Modern.Horizontal.TScale'
        )
        target_scale.pack(fill='x', pady=(5, 0))

        # Readability option
        readability_frame = tk.Frame(options_frame, bg=self.theme.COLORS['bg_secondary'])
        readability_frame.grid(row=0, column=1, sticky='ew', padx=(10, 0))

        tk.Label(
            readability_frame,
            text="Options:",
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w')

        self.maintain_readability_var = tk.BooleanVar(value=True)
        readability_cb = tk.Checkbutton(
            readability_frame,
            text="Maintain readability",
            variable=self.maintain_readability_var,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_secondary'],
            selectcolor=self.theme.COLORS['bg_tertiary'],
            activebackground=self.theme.COLORS['bg_secondary'],
            activeforeground=self.theme.COLORS['text_secondary'],
            relief='flat'
        )
        readability_cb.pack(anchor='w', pady=(10, 0))

        # Enhance button
        enhance_btn = ModernButton(
            input_card.content_frame,
            text="⚡ Enhance Password",
            command=self.enhance_password,
            style="primary"
        )
        enhance_btn.pack(fill='x')

        # Results section
        self.results_card = PasswordCard(parent, title="✨ Enhanced Password")
        self.results_card.pack(fill='both', expand=True, padx=20, pady=(10, 20))

        # Before/After comparison
        comparison_frame = tk.Frame(self.results_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        comparison_frame.pack(fill='x', pady=(0, 15))
        comparison_frame.columnconfigure(0, weight=1)
        comparison_frame.columnconfigure(1, weight=1)

        # Before column
        before_frame = tk.Frame(comparison_frame, bg=self.theme.COLORS['bg_secondary'])
        before_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))

        tk.Label(
            before_frame,
            text="Before:",
            font=self.theme.FONTS['subheading'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w')

        self.before_password_var = tk.StringVar()
        self.before_display = tk.Entry(
            before_frame,
            textvariable=self.before_password_var,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['accent_danger'],
            state='readonly',
            relief='flat',
            bd=5,
            readonlybackground=self.theme.COLORS['bg_tertiary']
        )
        self.before_display.pack(fill='x', pady=(5, 0))

        self.before_strength_var = tk.StringVar()
        tk.Label(
            before_frame,
            textvariable=self.before_strength_var,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_muted']
        ).pack(anchor='w', pady=(5, 0))

        # After column
        after_frame = tk.Frame(comparison_frame, bg=self.theme.COLORS['bg_secondary'])
        after_frame.grid(row=0, column=1, sticky='nsew', padx=(10, 0))

        tk.Label(
            after_frame,
            text="After:",
            font=self.theme.FONTS['subheading'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_primary']
        ).pack(anchor='w')

        self.after_password_var = tk.StringVar()
        self.after_display = tk.Entry(
            after_frame,
            textvariable=self.after_password_var,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['accent_secondary'],
            state='readonly',
            relief='flat',
            bd=5,
            readonlybackground=self.theme.COLORS['bg_tertiary']
        )
        self.after_display.pack(fill='x', pady=(5, 0))

        self.after_strength_var = tk.StringVar()
        tk.Label(
            after_frame,
            textvariable=self.after_strength_var,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_secondary'],
            fg=self.theme.COLORS['text_muted']
        ).pack(anchor='w', pady=(5, 0))

        # Enhancement details
        self.enhancement_details = tk.Text(
            self.results_card.content_frame,
            height=8,
            font=self.theme.FONTS['small'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_secondary'],
            relief='flat',
            bd=5,
            wrap='word',
            state='disabled'
        )
        self.enhancement_details.pack(fill='both', expand=True, pady=(15, 0))

        # Action buttons for enhanced password
        enh_action_frame = tk.Frame(self.results_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        enh_action_frame.pack(fill='x', pady=(15, 0))
        enh_action_frame.columnconfigure(0, weight=1)
        enh_action_frame.columnconfigure(1, weight=1)

        copy_enh_btn = ModernButton(
            enh_action_frame,
            text="📋 Copy Enhanced",
            command=self.copy_enhanced_password,
            style="success"
        )
        copy_enh_btn.grid(row=0, column=0, sticky='ew', padx=(0, 5))

        save_enh_btn = ModernButton(
            enh_action_frame,
            text="💾 Save Enhanced",
            command=self.save_enhanced_to_history,
            style="secondary"
        )
        save_enh_btn.grid(row=0, column=1, sticky='ew', padx=(5, 0))

    def create_history_interface(self, parent):
        """Create the password history interface."""
        # Header with stats
        header_card = PasswordCard(parent, title="📊 Password Statistics")
        header_card.pack(fill='x', padx=20, pady=(20, 10))

        stats_frame = tk.Frame(header_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        stats_frame.pack(fill='x')
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.columnconfigure(1, weight=1)
        stats_frame.columnconfigure(2, weight=1)

        self.total_generated_var = tk.StringVar(value="0")
        self.avg_strength_var = tk.StringVar(value="0")
        self.last_generated_var = tk.StringVar(value="Never")

        # Stats labels
        for i, (title, var) in enumerate([
            ("Total Generated", self.total_generated_var),
            ("Average Strength", self.avg_strength_var),
            ("Last Generated", self.last_generated_var)
        ]):
            stat_frame = tk.Frame(stats_frame, bg=self.theme.COLORS['bg_secondary'])
            stat_frame.grid(row=0, column=i, sticky='ew', padx=10)

            tk.Label(
                stat_frame,
                text=title,
                font=self.theme.FONTS['small'],
                bg=self.theme.COLORS['bg_secondary'],
                fg=self.theme.COLORS['text_muted']
            ).pack()

            tk.Label(
                stat_frame,
                textvariable=var,
                font=self.theme.FONTS['subheading'],
                bg=self.theme.COLORS['bg_secondary'],
                fg=self.theme.COLORS['accent_primary']
            ).pack()

        # History list
        history_card = PasswordCard(parent, title="📚 Password History")
        history_card.pack(fill='both', expand=True, padx=20, pady=(10, 20))

        # History controls
        controls_frame = tk.Frame(history_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        controls_frame.pack(fill='x', pady=(0, 10))

        search_frame = tk.Frame(controls_frame, bg=self.theme.COLORS['bg_secondary'])
        search_frame.pack(fill='x')
        search_frame.columnconfigure(0, weight=1)

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=self.theme.FONTS['body'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_primary'],
            insertbackground=self.theme.COLORS['accent_primary'],
            relief='flat',
            bd=5
        )
        search_entry.grid(row=0, column=0, sticky='ew', padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.filter_history)

        clear_btn = ModernButton(
            search_frame,
            text="🗑️ Clear All",
            command=self.clear_history,
            style="danger"
        )
        clear_btn.grid(row=0, column=1)

        # History listbox with custom styling
        list_frame = tk.Frame(history_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        list_frame.pack(fill='both', expand=True)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        self.history_listbox = tk.Listbox(
            list_frame,
            font=self.theme.FONTS['mono'],
            bg=self.theme.COLORS['bg_tertiary'],
            fg=self.theme.COLORS['text_primary'],
            selectbackground=self.theme.COLORS['accent_primary'],
            selectforeground=self.theme.COLORS['bg_primary'],
            relief='flat',
            bd=0,
            highlightthickness=0
        )
        self.history_listbox.grid(row=0, column=0, sticky='nsew')

        history_scroll = ttk.Scrollbar(
            list_frame,
            orient='vertical',
            command=self.history_listbox.yview
        )
        history_scroll.grid(row=0, column=1, sticky='ns')
        self.history_listbox.configure(yscrollcommand=history_scroll.set)

        # History actions
        hist_actions = tk.Frame(history_card.content_frame, bg=self.theme.COLORS['bg_secondary'])
        hist_actions.pack(fill='x', pady=(10, 0))
        hist_actions.columnconfigure(0, weight=1)
        hist_actions.columnconfigure(1, weight=1)

        use_btn = ModernButton(
            hist_actions,
            text="📋 Copy Selected",
            command=self.copy_from_history,
            style="success"
        )
        use_btn.grid(row=0, column=0, sticky='ew', padx=(0, 5))

        delete_btn = ModernButton(
            hist_actions,
            text="🗑️ Delete Selected",
            command=self.delete_from_history,
            style="danger"
        )
        delete_btn.grid(row=0, column=1, sticky='ew', padx=(5, 0))

    # Event handlers and core functionality
    def on_length_change(self, value):
        """Handle length scale change."""
        length = int(float(value))
        self.length_display.config(text=str(length))
        self.on_settings_change()

    def on_settings_change(self):
        """Handle settings change - auto-generate new password."""
        # Small delay to avoid too frequent updates
        if hasattr(self, '_settings_timer'):
            self.root.after_cancel(self._settings_timer)
        self._settings_timer = self.root.after(500, self.generate_new_password)

    def generate_new_password(self):
        """Generate a new password with current settings."""
        try:
            password = self.generator.generate_secure_password(
                length=self.length_var.get(),
                use_lowercase=self.char_vars['lowercase'].get(),
                use_uppercase=self.char_vars['uppercase'].get(),
                use_digits=self.char_vars['digits'].get(),
                use_symbols=self.char_vars['symbols'].get(),
                exclude_chars=self.exclude_chars_var.get(),
                exclude_similar=self.advanced_vars['exclude_similar'].get(),
                exclude_ambiguous=self.advanced_vars['exclude_ambiguous'].get(),
                ensure_strength=self.advanced_vars['ensure_strength'].get()
            )

            self.current_password.set(password)
            self.analyze_current_password(password)

        except ValueError as e:
            messagebox.showerror("Generation Error", str(e))

    def analyze_current_password(self, password):
        """Analyze and display password strength."""
        score, strength, feedback = self.analyzer.analyze_strength(password)

        # Update strength display
        self.strength_var.set(f"{strength} ({score}/100)")
        self.strength_progress.animate_to(score)

        # Color code the strength
        if score >= 80:
            color = self.theme.COLORS['success']
        elif score >= 60:
            color = self.theme.COLORS['accent_secondary']
        elif score >= 40:
            color = self.theme.COLORS['accent_warning']
        else:
            color = self.theme.COLORS['error']

        self.strength_display.config(fg=color)

        # Update feedback
        self.feedback_text.config(state='normal')
        self.feedback_text.delete(1.0, tk.END)

        if feedback:
            self.feedback_text.insert(tk.END, "💡 Suggestions for improvement:\n\n")
            for i, suggestion in enumerate(feedback, 1):
                self.feedback_text.insert(tk.END, f"{i}. {suggestion}\n")
        else:
            self.feedback_text.insert(tk.END, "✅ Excellent! This password meets all security criteria.")

        self.feedback_text.config(state='disabled')

    def copy_password(self):
        """Copy current password to clipboard."""
        password = self.current_password.get()
        if password:
            try:
                pyperclip.copy(password)
                self.show_notification("Password copied to clipboard!", "success")
            except Exception as e:
                messagebox.showerror("Clipboard Error", f"Failed to copy: {e}")
        else:
            messagebox.showwarning("No Password", "No password to copy!")

    def save_to_history(self):
        """Save current password to history."""
        password = self.current_password.get()
        if password:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            score, strength, _ = self.analyzer.analyze_strength(password)

            entry = {
                'password': password,
                'timestamp': timestamp,
                'strength': strength,
                'score': score,
                'type': 'Generated'
            }

            self.password_history.append(entry)
            self.update_history_display()
            self.update_statistics()
            self.show_notification("Password saved to history!", "success")
        else:
            messagebox.showwarning("No Password", "No password to save!")

    def show_notification(self, message, type="info"):
        """Show a temporary notification."""
        # Create notification window
        notification = tk.Toplevel(self.root)
        notification.wm_overrideredirect(True)

        # Position at top-right of main window
        x = self.root.winfo_x() + self.root.winfo_width() - 300
        y = self.root.winfo_y() + 50
        notification.geometry(f"280x60+{x}+{y}")

        # Style based on type
        if type == "success":
            bg_color = self.theme.COLORS['accent_secondary']
            fg_color = self.theme.COLORS['bg_primary']
        elif type == "error":
            bg_color = self.theme.COLORS['accent_danger']
            fg_color = self.theme.COLORS['bg_primary']
        else:
            bg_color = self.theme.COLORS['accent_primary']
            fg_color = self.theme.COLORS['bg_primary']

        notification.configure(bg=bg_color)

        label = tk.Label(
            notification,
            text=message,
            font=self.theme.FONTS['body'],
            bg=bg_color,
            fg=fg_color,
            padx=20,
            pady=15
        )
        label.pack(fill='both', expand=True)

        # Auto-hide after 3 seconds
        notification.after(3000, notification.destroy)

    def toggle_password_visibility(self):
        """Toggle password visibility in enhancer tab."""
        if self.show_password_var.get():
            self.ref_password_entry.config(show='')
        else:
            self.ref_password_entry.config(show='*')

    def enhance_password(self):
        """Enhance the reference password."""
        ref_password = self.reference_password.get().strip()
        if not ref_password:
            messagebox.showwarning("No Password", "Please enter a password to enhance!")
            return

        try:
            result = self.enhancer.enhance_password(
                ref_password,
                target_length=self.target_length_var.get(),
                maintain_readability=self.maintain_readability_var.get()
            )

            # Update before/after display
            self.before_password_var.set(result['original'])
            self.after_password_var.set(result['enhanced'])

            # Update strength displays
            orig_analysis = result['original_analysis']
            enh_analysis = result['enhanced_analysis']

            self.before_strength_var.set(f"{orig_analysis['strength']} ({orig_analysis['score']}/100)")
            self.after_strength_var.set(f"{enh_analysis['strength']} ({enh_analysis['score']}/100)")

            # Update enhancement details
            self.enhancement_details.config(state='normal')
            self.enhancement_details.delete(1.0, tk.END)

            details = f"🔧 Enhancement Results:\n\n"
            details += f"• Improvement: +{result['improvement']} points\n"
            details += f"• Strategies used: {', '.join(result['strategies_used'])}\n\n"

            details += f"📊 Original Analysis:\n"
            if orig_analysis['feedback']:
                for feedback in orig_analysis['feedback'][:3]:
                    details += f"  ⚠️ {feedback}\n"
            else:
                details += "  ✅ No major issues found\n"

            details += f"\n📈 Enhanced Analysis:\n"
            if enh_analysis['feedback']:
                for feedback in enh_analysis['feedback'][:3]:
                    details += f"  💡 {feedback}\n"
            else:
                details += "  ✅ Excellent security!\n"

            self.enhancement_details.insert(tk.END, details)
            self.enhancement_details.config(state='disabled')

            self.show_notification("Password enhanced successfully!", "success")

        except Exception as e:
            messagebox.showerror("Enhancement Error", f"Failed to enhance password: {e}")

    def copy_enhanced_password(self):
        """Copy enhanced password to clipboard."""
        password = self.after_password_var.get()
        if password:
            try:
                pyperclip.copy(password)
                self.show_notification("Enhanced password copied!", "success")
            except Exception as e:
                messagebox.showerror("Clipboard Error", f"Failed to copy: {e}")
        else:
            messagebox.showwarning("No Password", "No enhanced password to copy!")

    def save_enhanced_to_history(self):
        """Save enhanced password to history."""
        password = self.after_password_var.get()
        if password:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            score, strength, _ = self.analyzer.analyze_strength(password)

            entry = {
                'password': password,
                'timestamp': timestamp,
                'strength': strength,
                'score': score,
                'type': 'Enhanced'
            }

            self.password_history.append(entry)
            self.update_history_display()
            self.update_statistics()
            self.show_notification("Enhanced password saved!", "success")
        else:
            messagebox.showwarning("No Password", "No enhanced password to save!")

    def update_history_display(self):
        """Update the history listbox display."""
        self.history_listbox.delete(0, tk.END)

        # Filter based on search
        search_term = self.search_var.get().lower()
        filtered_history = []

        for entry in reversed(self.password_history):  # Show newest first
            if not search_term or search_term in entry['password'].lower() or search_term in entry['type'].lower():
                filtered_history.append(entry)

        # Add entries to listbox
        for entry in filtered_history:
            display_text = f"[{entry['type']}] {entry['timestamp']} - {entry['password']} ({entry['strength']})"
            self.history_listbox.insert(tk.END, display_text)

    def update_statistics(self):
        """Update password statistics."""
        total = len(self.password_history)
        self.total_generated_var.set(str(total))

        if total > 0:
            avg_score = sum(entry['score'] for entry in self.password_history) / total
            self.avg_strength_var.set(f"{avg_score:.1f}/100")

            latest = self.password_history[-1]['timestamp']
            self.last_generated_var.set(latest.split()[1])  # Show time only
        else:
            self.avg_strength_var.set("0/100")
            self.last_generated_var.set("Never")

    def filter_history(self, event=None):
        """Filter history based on search term."""
        self.update_history_display()

    def copy_from_history(self):
        """Copy selected password from history."""
        selection = self.history_listbox.curselection()
        if selection:
            index = selection[0]
            # Get the actual password from history (accounting for filtering)
            search_term = self.search_var.get().lower()
            filtered_entries = []

            for entry in reversed(self.password_history):
                if not search_term or search_term in entry['password'].lower() or search_term in entry['type'].lower():
                    filtered_entries.append(entry)

            if index < len(filtered_entries):
                password = filtered_entries[index]['password']
                try:
                    pyperclip.copy(password)
                    self.show_notification("Password copied from history!", "success")
                except Exception as e:
                    messagebox.showerror("Clipboard Error", f"Failed to copy: {e}")
        else:
            messagebox.showwarning("No Selection", "Please select a password from history!")

    def delete_from_history(self):
        """Delete selected password from history."""
        selection = self.history_listbox.curselection()
        if selection:
            if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this password?"):
                index = selection[0]
                # Find and remove the actual entry
                search_term = self.search_var.get().lower()
                filtered_entries = []
                original_indices = []

                for i, entry in enumerate(reversed(self.password_history)):
                    if not search_term or search_term in entry['password'].lower() or search_term in entry['type'].lower():
                        filtered_entries.append(entry)
                        original_indices.append(len(self.password_history) - 1 - i)

                if index < len(original_indices):
                    del self.password_history[original_indices[index]]
                    self.update_history_display()
                    self.update_statistics()
                    self.show_notification("Password deleted!", "success")
        else:
            messagebox.showwarning("No Selection", "Please select a password to delete!")

    def clear_history(self):
        """Clear all password history."""
        if self.password_history and messagebox.askyesno("Confirm Clear", "Are you sure you want to clear all password history?"):
            self.password_history.clear()
            self.update_history_display()
            self.update_statistics()
            self.show_notification("History cleared!", "success")


def main():
    """Main function to run the modern password generator."""
    try:
        root = tk.Tk()
        app = ModernPasswordGeneratorApp(root)
        root.mainloop()
    except ImportError as e:
        if "pyperclip" in str(e):
            print("Error: pyperclip module is required for clipboard functionality.")
            print("Install it using: pip install pyperclip")
        else:
            print(f"Import error: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()
