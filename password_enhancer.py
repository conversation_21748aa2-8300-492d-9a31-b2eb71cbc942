#!/usr/bin/env python3
"""
Password Enhancement Module
Transforms weak passwords into strong ones while maintaining recognizable patterns.
"""

import re
import random
import string
from password_generator_gui import PasswordStrengthAnalyzer


class PasswordEnhancer:
    """Enhances existing passwords to make them stronger while maintaining familiarity."""
    
    def __init__(self):
        self.analyzer = PasswordStrengthAnalyzer()
        
        # Character substitution mappings for leet speak and security
        self.char_substitutions = {
            'a': ['@', '4', 'A'],
            'e': ['3', 'E', '€'],
            'i': ['1', '!', 'I'],
            'o': ['0', 'O', '°'],
            's': ['$', '5', 'S'],
            't': ['7', 'T', '+'],
            'l': ['1', 'L', '|'],
            'g': ['9', 'G', '6'],
            'b': ['8', 'B', '6'],
            'z': ['2', 'Z', '7']
        }
        
        # Common password patterns and their improvements
        self.pattern_improvements = {
            r'(\d+)$': self._enhance_trailing_numbers,
            r'^(\w+)(\d+)$': self._enhance_word_number_pattern,
            r'(\w+)': self._enhance_word_segments,
        }
        
        # Symbols to add for complexity
        self.complexity_symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        
    def enhance_password(self, original_password, target_length=None, maintain_readability=True):
        """
        Enhance a password to make it stronger while maintaining recognizable elements.
        
        Args:
            original_password (str): The original password to enhance
            target_length (int): Desired length for enhanced password
            maintain_readability (bool): Whether to keep some readable elements
            
        Returns:
            dict: Enhancement results with original, enhanced password, and analysis
        """
        if not original_password:
            raise ValueError("Original password cannot be empty")
        
        # Analyze original password
        orig_score, orig_strength, orig_feedback = self.analyzer.analyze_strength(original_password)
        
        # Determine target length
        if target_length is None:
            target_length = max(12, len(original_password) + 4)
        
        # Apply enhancement strategies
        enhanced = self._apply_enhancement_strategies(
            original_password, target_length, maintain_readability
        )
        
        # Analyze enhanced password
        new_score, new_strength, new_feedback = self.analyzer.analyze_strength(enhanced)
        
        # If still not strong enough, apply additional strengthening
        if new_score < 70:
            enhanced = self._apply_additional_strengthening(enhanced, target_length)
            new_score, new_strength, new_feedback = self.analyzer.analyze_strength(enhanced)
        
        return {
            'original': original_password,
            'enhanced': enhanced,
            'original_analysis': {
                'score': orig_score,
                'strength': orig_strength,
                'feedback': orig_feedback
            },
            'enhanced_analysis': {
                'score': new_score,
                'strength': new_strength,
                'feedback': new_feedback
            },
            'improvement': new_score - orig_score,
            'strategies_used': self._get_strategies_used(original_password, enhanced)
        }
    
    def _apply_enhancement_strategies(self, password, target_length, maintain_readability):
        """Apply various enhancement strategies to improve password strength."""
        enhanced = password
        
        # Strategy 1: Character substitution (leet speak)
        if maintain_readability:
            enhanced = self._apply_smart_substitutions(enhanced)
        else:
            enhanced = self._apply_aggressive_substitutions(enhanced)
        
        # Strategy 2: Case variation
        enhanced = self._improve_case_variation(enhanced)
        
        # Strategy 3: Add complexity symbols
        enhanced = self._add_complexity_symbols(enhanced)
        
        # Strategy 4: Extend to target length
        enhanced = self._extend_to_target_length(enhanced, target_length)
        
        # Strategy 5: Break up patterns
        enhanced = self._break_common_patterns(enhanced)
        
        return enhanced
    
    def _apply_smart_substitutions(self, password):
        """Apply character substitutions that maintain readability."""
        result = list(password.lower())
        
        # Apply substitutions to some characters, not all
        for i, char in enumerate(result):
            if char in self.char_substitutions and random.random() < 0.4:
                substitutes = self.char_substitutions[char]
                result[i] = random.choice(substitutes)
        
        return ''.join(result)
    
    def _apply_aggressive_substitutions(self, password):
        """Apply more aggressive character substitutions."""
        result = list(password.lower())
        
        for i, char in enumerate(result):
            if char in self.char_substitutions and random.random() < 0.7:
                substitutes = self.char_substitutions[char]
                result[i] = random.choice(substitutes)
        
        return ''.join(result)
    
    def _improve_case_variation(self, password):
        """Improve case variation in the password."""
        result = list(password)
        
        # Ensure we have both upper and lower case
        has_upper = any(c.isupper() for c in result)
        has_lower = any(c.islower() for c in result)
        
        if not has_upper:
            # Make some letters uppercase
            for i in range(len(result)):
                if result[i].isalpha() and random.random() < 0.3:
                    result[i] = result[i].upper()
        
        if not has_lower:
            # Make some letters lowercase
            for i in range(len(result)):
                if result[i].isupper() and random.random() < 0.3:
                    result[i] = result[i].lower()
        
        return ''.join(result)
    
    def _add_complexity_symbols(self, password):
        """Add symbols to increase complexity."""
        if not any(c in self.complexity_symbols for c in password):
            # Add symbols at strategic positions
            positions = [0, len(password)//2, len(password)]
            position = random.choice(positions)
            symbol = random.choice(self.complexity_symbols)
            
            if position == 0:
                password = symbol + password
            elif position == len(password):
                password = password + symbol
            else:
                password = password[:position] + symbol + password[position:]
        
        return password
    
    def _extend_to_target_length(self, password, target_length):
        """Extend password to target length intelligently."""
        if len(password) >= target_length:
            return password
        
        needed = target_length - len(password)
        
        # Add meaningful extensions
        extensions = []
        
        # Add year or numbers
        if needed >= 4:
            extensions.append(str(random.randint(2020, 2030)))
            needed -= 4
        
        # Add symbols
        while needed > 0 and len(extensions) < 3:
            extensions.append(random.choice(self.complexity_symbols))
            needed -= 1
        
        # Add random characters if still needed
        while needed > 0:
            char_type = random.choice(['upper', 'lower', 'digit', 'symbol'])
            if char_type == 'upper':
                extensions.append(random.choice(string.ascii_uppercase))
            elif char_type == 'lower':
                extensions.append(random.choice(string.ascii_lowercase))
            elif char_type == 'digit':
                extensions.append(random.choice(string.digits))
            else:
                extensions.append(random.choice(self.complexity_symbols))
            needed -= 1
        
        # Insert extensions at various positions
        result = password
        for ext in extensions:
            pos = random.randint(0, len(result))
            result = result[:pos] + ext + result[pos:]
        
        return result
    
    def _break_common_patterns(self, password):
        """Break up common weak patterns."""
        # Break up sequential characters
        result = password
        
        # Insert random characters to break sequences
        if re.search(r'(012|123|234|345|456|567|678|789)', result):
            # Find and break numeric sequences
            for match in re.finditer(r'(\d{3,})', result):
                start, end = match.span()
                middle = (start + end) // 2
                symbol = random.choice(['!', '@', '#', '$'])
                result = result[:middle] + symbol + result[middle:]
                break
        
        return result
    
    def _apply_additional_strengthening(self, password, target_length):
        """Apply additional strengthening if password is still weak."""
        result = password
        
        # Ensure minimum character variety
        has_upper = any(c.isupper() for c in result)
        has_lower = any(c.islower() for c in result)
        has_digit = any(c.isdigit() for c in result)
        has_symbol = any(c in self.complexity_symbols for c in result)
        
        additions = []
        if not has_upper:
            additions.append(random.choice(string.ascii_uppercase))
        if not has_lower:
            additions.append(random.choice(string.ascii_lowercase))
        if not has_digit:
            additions.append(random.choice(string.digits))
        if not has_symbol:
            additions.append(random.choice(self.complexity_symbols))
        
        # Add missing character types
        for addition in additions:
            pos = random.randint(0, len(result))
            result = result[:pos] + addition + result[pos:]
        
        # Extend if too short
        while len(result) < target_length:
            char_type = random.choice(['upper', 'lower', 'digit', 'symbol'])
            if char_type == 'upper':
                char = random.choice(string.ascii_uppercase)
            elif char_type == 'lower':
                char = random.choice(string.ascii_lowercase)
            elif char_type == 'digit':
                char = random.choice(string.digits)
            else:
                char = random.choice(self.complexity_symbols)
            
            pos = random.randint(0, len(result))
            result = result[:pos] + char + result[pos:]
        
        return result
    
    def _get_strategies_used(self, original, enhanced):
        """Determine which enhancement strategies were used."""
        strategies = []
        
        if len(enhanced) > len(original):
            strategies.append("Length extension")
        
        if any(c in self.complexity_symbols for c in enhanced) and \
           not any(c in self.complexity_symbols for c in original):
            strategies.append("Symbol addition")
        
        if sum(1 for c in enhanced if c.isupper()) > sum(1 for c in original if c.isupper()):
            strategies.append("Case variation")
        
        # Check for character substitutions
        orig_lower = original.lower()
        enhanced_lower = enhanced.lower()
        if orig_lower != enhanced_lower:
            strategies.append("Character substitution")
        
        return strategies
    
    def _enhance_trailing_numbers(self, match):
        """Enhance trailing numbers pattern."""
        numbers = match.group(1)
        # Convert to more complex pattern
        enhanced = ""
        for digit in numbers:
            if random.random() < 0.5:
                enhanced += digit
            else:
                enhanced += random.choice(['!', '@', '#']) + digit
        return enhanced
    
    def _enhance_word_number_pattern(self, match):
        """Enhance word followed by numbers pattern."""
        word = match.group(1)
        numbers = match.group(2)
        
        # Apply substitutions to word
        enhanced_word = self._apply_smart_substitutions(word)
        enhanced_numbers = self._enhance_trailing_numbers(match)
        
        return enhanced_word + enhanced_numbers
    
    def _enhance_word_segments(self, match):
        """Enhance word segments."""
        word = match.group(1)
        return self._apply_smart_substitutions(word)


def demo_password_enhancement():
    """Demonstrate password enhancement capabilities."""
    enhancer = PasswordEnhancer()
    
    test_passwords = [
        "password",
        "john123",
        "mypassword",
        "admin2024",
        "welcome",
        "qwerty123",
        "letmein",
        "password123",
        "user1234",
        "test"
    ]
    
    print("🔐 PASSWORD ENHANCEMENT DEMONSTRATION")
    print("=" * 60)
    
    for original in test_passwords:
        try:
            result = enhancer.enhance_password(original, target_length=16)
            
            print(f"\nOriginal:  {original}")
            print(f"Enhanced:  {result['enhanced']}")
            print(f"Strength:  {result['original_analysis']['strength']} → {result['enhanced_analysis']['strength']}")
            print(f"Score:     {result['original_analysis']['score']}/100 → {result['enhanced_analysis']['score']}/100 (+{result['improvement']})")
            print(f"Strategies: {', '.join(result['strategies_used'])}")
            
        except Exception as e:
            print(f"Error enhancing '{original}': {e}")


if __name__ == "__main__":
    demo_password_enhancement()
